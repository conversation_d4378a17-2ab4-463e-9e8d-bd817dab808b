class Integrations::Mosyle::FetchData
  include ReadReplicaDb

  attr_accessor :client, :error

  def initialize(company_id, config = nil)
    @config = config
    @company_id = company_id
    @intg_id = set_read_replica_db do
      Integration.find_by_name('mosyle').id
    end
    @requests_count = 0
  end

  def token(mosyle_data)
    auth_params = {
      email: mosyle_data['username'],
      password: mosyle_data['password'],
    }

    url = "https://businessapi.mosyle.com/v1/login"
    response = HTTParty.post(url,
                             body: auth_params.to_json,
                             headers: {
                              'Content-Type': 'application/json',
                              'accessToken' => mosyle_data['access_token']
                             })
    log_event(response, 'token', { mosyle_data: mosyle_data })

    authorization_header = response.headers['authorization']
    expires_header = response.headers['expires']

    response.parsed_response.merge({
      'token' => authorization_header,
      'expires' => expires_header
    })
  end

  def get_devices(page, os_type = 'ios')
    body = {
      operation: 'list',
      options: {
        os: os_type,
        page: page,
        page_size: 100
      }
    }
    fetch_inventory(body, "get_#{os_type}_devices")
  end

  def fetch_inventory(body, action)
    response = make_api_call(body, action)
    return response&.parsed_response if response.success?

    if response.response.message.present?
      self.error = response.response.message
      raise response.response.message
    elsif response.parsed_response['errors'].present?
      error_description = response.parsed_response['errors'][0]['description']
      self.error = error_description
      raise
    end
  end

  def refresh_access_token
    response = token(@config)
    if response && response['token']
      @config.update(
        refresh_token: response['token'],
        expires_in: response['expires'],
        skip_callbacks: true
      )
      @config.reload
    end
    @config
  end

  def check_token_expiry
    if @requests_count == 30
      refresh_access_token
      @requests_count = 0
    end
    @requests_count += 1
  end

  def make_api_call(params, api_type)
    check_token_expiry

    url = "https://businessapi.mosyle.com/v1/devices"
    headers = {
      Authorization: "#{@config.refresh_token}",
      'accessToken': "#{@config.access_token}",
      'Content-Type': 'application/json',
    }

    response = HTTParty.post(url, body: params.to_json, headers: headers)
    if response&.parsed_response['error']
      self.error = response.parsed_response['error']['message']
    end
    log_event(response, api_type, { endpoint: url })
    response
  end

  def log_event(response, api_type, additional_details = {}, excep = nil)
    status = response.present? && response.code == 200 ? :success : :error
    api_response = { code: response.code, message: response.message, body: response.body }.to_s if response
    log_params = {
      api_type: api_type,
      class_name: self.class,
      integration_id: @intg_id,
      company_id: @company_id,
      status: status,
      detail: details.merge(additional_details || {}),
      activity: 1,
      response: api_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil,
    }
    if Rails.env.test?
      Logs::ApiEvent.create(log_params)
    else
      LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
    end
  end

  def ok?
    error.blank?
  end

  def details
    @config ? { token: @config.refresh_token, expires_in: @config.expires_in } : {}
  end
end
