class CompanyIntegration < ApplicationRecord
  include CompanyCache
  include DestroyIntegrationData
  include DiscoveryToolsLogs

  validates :company_id, :integration_id, :integrable_id, :integrable_type, presence: true

  attr_accessor :first_time_flag, :company_user_id

  belongs_to :company
  belongs_to :integration
  belongs_to :integrable, polymorphic: true, dependent: :destroy

  has_many :general_transactions
  has_many :cloud_usage_transactions
  has_many :predicted_cloud_transactions
  has_many :app_usages, class_name: 'Integrations::AppUsage', foreign_key: 'company_integration_id'
  has_many :asset_sources, dependent: :nullify

  after_commit :create_integration_status_log, on: [:create, :update]
  after_destroy :create_integration_destroy_log

  delegate :name, to: :integration

  enum sync_status: [:successful, :pending, :failed, :in_progress]

  scope :active, -> { where(active: true) }

  CONNECTORS_CONFIGS = {
    "Integrations::Meraki::Config": "meraki",
    "Integrations::AwsAssets::Config": "aws",
    "Integrations::JamfPro::Config": "jamf_pro",
    "Integrations::Kandji::Config": "kandji",
    "Integrations::MsIntuneAssets::Config": "ms_intune",
    "Integrations::Kaseya::Config": "kaseya",
    "Integrations::AzureAdAssets::Config": "azure_ad_devices",
    "Integrations::AzureAssets::Config": "azure",
    "Integrations::Ubiquiti::Config": "ubiquiti",
    "Integrations::GoogleAssets::Config": "google",
    "Integrations::Mosyle::Config": "mosyle"
  }

  def integration_url
    integration_types = {
      vendors_integrations: %w[
        quickbooks xero plaid expensify sage_accounting bill netsuite
        sage_intacct aws azure microsoft salesforce okta one_login gsuite
      ],
      assets_integrations: %w[meraki ubiquiti gcp aws_assets azure_assets google_assets ms_intune kaseya kandji jamf_pro mosyle google_workspace],
      staff_integrations: %w[gsuite_ad azure_ad]
    }

    integration_paths = {
      "vendors_integrations": "#{protocol}#{company.subdomain}.#{Rails.application.credentials.root_domain}#{port}/vendors/sync_accounts",
      "assets_integrations": "#{protocol}#{company.subdomain}.#{Rails.application.credentials.root_domain}#{port}/managed_assets/discovery_tools/connectors",
      "staff_integrations": "#{protocol}#{company.subdomain}.#{Rails.application.credentials.root_domain}#{port}/company/users/sync_and_download",
    }

    integration_type = integration_types.map { |key, value| key if value.any? { |name| name == self.integration.name } }.compact.first
    integration_paths[integration_type] ? integration_paths[integration_type] : integration_paths[:vendors_integrations]
  end

  def port
    Rails.application.credentials.port ? ":#{Rails.application.credentials.port}" : nil
  end

  def protocol
    Rails.env.development? || Rails.env.test? ? "http://" : "https://"
  end
  
  def delete_cloud_usage_transactions_in_batches
    cloud_usage_transactions.in_batches(of: 3000).destroy_all
  end

  def create_integration_status_log
    if saved_change_to_id?
      log_event(:credentials_added, :successful)

    elsif is_last_synced_present?
      event = first_time_flag ? :sync : :resync
      log_event(event, self.sync_status.to_sym)

    elsif active_changed_to_false?
      log_event(:deactivated, :successful)
    end
  end

  def create_integration_destroy_log
    log_event(:deleted, :successful)
  end

  def is_last_synced_present?
    self.saved_changes.key?(:last_synced_at)
  end

  def active_changed_to_false?
    self.saved_changes[:active]&.last == false
  end

  def log_event(action, status)
    key = self.integrable_type&.to_sym
    return unless CONNECTORS_CONFIGS.key?(key)
    connector_value = CONNECTORS_CONFIGS[key]

    create_asset_connector_log(
      action,
      connector_value.to_sym,
      [],
      status,
      company_user_id,
      company.id
    )
  end
end
