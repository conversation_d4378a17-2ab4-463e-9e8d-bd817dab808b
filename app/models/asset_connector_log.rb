class AssetConnectorLog < ActiveRecord::Base
  belongs_to :company
  belongs_to :company_user

  enum action: [:sync, :resync, :deactivated, :deleted, :credentials_updated, :credentials_added, :activated] # Add more values as needed
  enum connector_name: [:meraki, :ubiquiti, :kaseya, :kandji, :azure_ad_devices, :azure, :aws, :google, :ms_intune, :jamf_pro, :probe, :agent, :mosyle] # Add more values as needed
  enum status: [:successful, :failed]
end
