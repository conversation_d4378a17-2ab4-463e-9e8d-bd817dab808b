import { mapMutations, mapGetters } from 'vuex';
import MomentTimezone from 'mixins/moment-timezone';

export default {
  mixins: [MomentTimezone],
  computed: {
    ...mapGetters(['viewType', 'selectedCardDataItems']),
    ...mapGetters('GlobalStore', ['selectedCardData']),
    currentManagedAsset() {
      return this.currentAsset || this.asset;
    },
    updatedAt() {
      if (this.currentManagedAsset?.updatedAt) {
        return this.timezoneDatetime(this.currentManagedAsset.updatedAt, Vue.prototype.$timezone);
      }
      return null;
    },
    showCloudAssetAttrs() {
      return ["aws", "azure", "google"].some(source => this.currentManagedAsset.sources.includes(source));
    },
    hasComputerDetail() {
      return this.currentManagedAsset.hardwareDetailType === "ComputerDetail";
    },
    isAgentOrSelfOnBording() {
      return this.hasComputerDetail && ["agent", "selfonboarding"].some(source => this.currentManagedAsset.sources.includes(source));
    },
    isProbeAndComputer() {
      return this.currentManagedAsset.sources.includes("probe") && this.hasComputerDetail;
    },
    showSystemDetails() {
      return (this.isMerakiDevice || this.isProbeAndSwitch || this.isProbeAndComputer || this.isAgentOrSelfOnBording || this.isProbeAndPrinter || this.isKaseyaAsset);
    },
    showDetails() {
      return !this.hasIntegratedSource || (this.hasIntegratedSource && !!this.currentManagedAsset.details);
    },
    hasIntegratedSource() {
      return !!this.currentManagedAsset.sources.find(source => this.integratedSources.includes(source));
    },
    isProbeAndPrinter() {
      return this.currentManagedAsset.sources.includes("probe") && this.currentManagedAsset.hardwareDetailType === "PrinterDetail";
    },
    isProbeAndSwitch() {
      return this.currentManagedAsset.sources.includes("probe") && this.currentManagedAsset.assetType.toLowerCase() === "switch";
    },
    isMerakiDevice() {
      const deviceType = ['switch', 'wap', 'firewall'];
      return (this.currentManagedAsset.sources.includes('meraki') && deviceType.includes(this.currentManagedAsset.assetType.toLowerCase()));
    },
    isKaseyaAsset() {
      return this.currentManagedAsset.source === 'kaseya' && this.currentManagedAsset.assetType !== 'Printer';
    },
    assetSource() {
      return this.currentManagedAsset?.source;
    },
    selectedItemsLookUp() {
      const data = this.fullWidth && this.newWindow ? this.selectedCardData : this.selectedCardDataItems;
      return data.reduce((lookUp, item) => {
        lookUp[item.name] = true;
        return lookUp;
      }, {});
    },
    hideClientStats() {
      const intgs = ['kaseya', 'kandji', 'mosyle'];
      return intgs.includes(this.intgDetails.name);
    },
  },
  methods: {
    ...mapMutations(['setViewType']),
    async assetPreferencesCall() {
      if (this.viewType === "list") {
        await this.$store.dispatch('fetchAssetPreferences');
      }
    },
    viewTypeFromCookie() {
      const viewType = (`; ${document.cookie}`).split(`; view_type_assets=`).pop().split(';')[0];
      this.setViewType(viewType || 'grid');
    },
  },
};
