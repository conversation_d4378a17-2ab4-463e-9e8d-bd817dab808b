import { mapGetters } from 'vuex';

export default {
  computed: {
    ...mapGetters(['currentContributorIds']),
    isBold() {
      if (!this.$superAdminUser) {
        const ticket = this.ticket || this.currentHelpTicket;
        const assignedToField = ticket?.fields?.assignedTo || ticket?.assignedTo;
        if ((assignedToField || ticket?.assignedUserIds) && this.currentCompanyUser) {
          const assignIds = assignedToField ? assignedToField.map(obj => obj.id) : ticket.assignedUserIds;
          return !ticket.isSeen && (assignIds.some(id => this.currentContributorIds.includes(id)));
        }
      }
      return false;
    },
    isAgent() {
      return this.currentCompanyUser?.isWorkspaceAgent;
    },
    leftPositionClass() {
      return Object.keys($permissions).length > 1 ? 'sidebar-left-open' : 'sidebar-left-closed';
    },
  },
  methods: {
    isFieldDisabled(ticket, field) {
      if (!ticket.canWrite) {
        return true;
      }

      if (field === 'status') {
        return ticket.fields.status.disabled;
      } else if (field === 'assignedTo') {
        return ticket.assignedTo && ticket.assignedTo[0].disabled;
      }
      return false;
    },
  },
};
