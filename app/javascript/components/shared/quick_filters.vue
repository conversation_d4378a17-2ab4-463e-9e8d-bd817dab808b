<template>
  <div 
    v-if="options.length" 
    :key="displayStyle"
  >
    <Teleport
      :to="`${teleportClass}`"
      :disabled="!teleportClass"
    >
      <div :class="{ 'd-flex align-items-baseline' : isHorizontalStyle}">
        <div
          v-if="showLeftScrollButton && isHorizontalStyle && isContentOverflowing"
          id="leftButtonDiv"
          class="scroll-btn-gradient scroll-btn-right-gradient left-button-css cursor-pointer position-relative"
          :class="{ 'show-gradient': true }"
          @click="scrollTo('left')"
        >
          <button class="custom-border scroll-btn scroll-btn-right position-absolute rounded-circle bg-light btn-outline-light border-0 pt-1 clickable">
            <i class="nulodgicon-chevron-left"/>
          </button>
        </div>
        <div :class="{'sub-sub-menu': isVerticalStyle, 'subpage-menu border-faded': isHorizontalStyle, 'no-click': mspFlag}">
          <div
            v-if="isVerticalStyle"
            class="d-flex align-items-center justify-content-between"
            :class="{'clickable': allowAccordion}"
            @click="allowAccordion && toggleShow()"
          >
            <h6 class="text-muted smallest mb-0">
              {{ heading }}
            </h6>
            <div class="d-flex align-items-center">
              <i
                v-if="allowAccordion"
                class="clickable small text-muted mr-2"
                :class="arrowDirection"
              />
              <i
                v-if="isVerticalStyle && !isRead"
                v-tooltip="'Edit Quick View'"
                class="nulodgicon-edit clickable h6 mb-0 mr-2"
                @click.stop.prevent="openFiltersListModal"
              />
              <i
                v-if="isVerticalStyle && !isRead"
                v-tooltip="'Create Quick View'"
                class="nulodgicon-android-add-circle clickable h5 mb-0 mr-1"
                @click.stop.prevent="openFilterModal"
              />
            </div>
          </div>
          <transition 
            name="quickFadeHeight" 
            mode="out-in"
          >
            <div
              id="quickViewContainerId"
              :class="{
                'overflow-auto': isScrollable,
                'templates-width text-nowrap scrollable--hidden-bar overflow-auto d-flex': isHorizontalStyle,
              }"
              :style="quickFiltersStyle"
              @scroll="onScroll"
            >
              <span
                v-for="(quickFilter, index) in computedOptions"
                :key="index"
                :class="{ 'submenu_conditional' : isVerticalStyle }"
              >
                <span
                  class="subpage-menu__item clickable"
                  :class="{
                    'subpage-menu__item--with-split-button': showAddNewViewButton(quickFilter),
                    'disabled': !addNewViewButtonActive(quickFilter),
                    'router-link-exact-active bg-helpdesk text-white': isActive(quickFilter),
                    'text-faded-light--unthemed': !isActive(quickFilter),
                    'more-items': isVerticalStyle && index > 5,
                  }"
                  :data-tc-quick-view-option="quickFilter.name"
                  @click="quickFilterClicked(quickFilter)"
                >
                  <span>{{ quickFilter.name }}</span>
                  <span
                    v-if="isVerticalStyle"
                    class="float-right"
                  >
                    {{ quickFilter.ticketCount }}
                  </span>
                </span>
                <i
                  v-if="showAddNewViewButton(quickFilter)"
                  v-tooltip="'Create New Custom Ticket View'"
                  class="nulodgicon-plus-round subpage-menu__item subpage-menu__item--split-button btn-flat clickable"
                  :class="{'disabled2': !addNewViewButtonActive(quickFilter), 'router-link-exact-active': addNewViewButtonActive(quickFilter)}"
                />
                <span
                  v-if="isVerticalStyle && computedOptions.length > 6 && index == (computedOptions.length -1)"
                  class="show-more"
                  @click="toggleMenu"
                >
                  <i class="nulodgicon-chevron-down" />
                </span>
              </span>
            </div>
          </transition>
        </div>
        <span v-if="isHorizontalStyle">
          <div
            v-if="showRightScrollButton && isContentOverflowing"
            id="rightButtonDiv"
            class="scroll-btn-gradient scroll-btn-right-gradient right-button-css cursor-pointer position-relative"
            :class="{ 'show-gradient': true, 'right-top-button-css': isRead }"
            @click="scrollTo('right')"
          >
            <button class="custom-border scroll-btn scroll-btn-right position-absolute rounded-circle bg-light btn-outline-light border-0 pt-1 clickable">
              <i class="nulodgicon-chevron-right" />
            </button>
          </div>
          <i
            v-if="!isRead"
            v-tooltip="'Edit Quick View'"
            class="nulodgicon-edit clickable h6 mb-0 ml-1 text-faded-light--unthemed"
            data-tc-icon="edit quick view"
            @click.stop.prevent="openFiltersListModal"
          />
          <i
            v-if="!isRead"
            v-tooltip="'Create Quick View'"
            class="nulodgicon-android-add-circle align-middle clickable mb-0 ml-1 text-faded-light--unthemed"
            data-tc-icon="create quick view"
            @click.stop.prevent="openFilterModal"
          />
        </span>
      </div>
    </Teleport>
    <Teleport to="body">
      <quick-view-filter-form
        ref="quickViewFilterForm"
        :prev-data="quickViewOptions"
        :quick-view-names="quickViewNames"
        @fetch-quick-filters="$emit('fetch-quick-filters')"
      />
    </Teleport>
    <quick-filters-list
      ref="quickFiltersList"
      :filters="quickViewOptions"
      @update:filters="quickViewOptions = $event"
      @open-filter-modal="openFilterModal"
      @updated-options="updateOptions"
      @fetch-quick-filters="$emit('fetch-quick-filters')"
    />
  </div>
</template>

<script>
  import _cloneDeep from 'lodash/cloneDeep';
  import mspHelper from 'mixins/msp_helper';
  import permissionsHelper from 'mixins/permissions_helper';
  import QuickViewFilterForm from './quick_filter_form.vue';
  import QuickFiltersList from './quick_filters_list.vue';

  export default {
    components: {
      QuickViewFilterForm,
      QuickFiltersList,
    },
    mixins: [permissionsHelper, mspHelper],
    props: {
      options: {
        type: Array,
        dafault: [],
        required: true,
      },
      loading: {
        type: Boolean,
        default: false,
        required: false,
      },
      allowCustomAdditions: {
        type: Boolean,
        default: false,
        required: false,
      },
      disableRightScroll: {
        type: Boolean,
        default: true,
        required: false,
      },
      showActiveCheckmark: {
        type: Boolean,
        default: false,
        required: false,
      },
      showHeadingIcon: {
        type: Boolean,
        default: false,
        required: false,
      },
      heading: {
        type: String,
        default: '',
        required: false,
      },
      displayStyle: {
        type: String,
        default: 'horizontal',
        required: false,
      },
      teleportClass: {
        type: String,
        default: '',
        required: false,
      },
      allowAccordion: {
        type: Boolean,
        default: false,
        required: false,
      },
      allowSorting: {
        type: Boolean,
        default: false,
        required: false,
      },
    },
    data() {
      return {
        showCondition: true,
        activeFilter: '',
        customTicketViewLabel: 'Custom Ticket View',
        quickViewOptions: _cloneDeep(this.options),
        isScrollable: false,
        showLeftScrollButton: false,
        showRightScrollButton: true,
        observer: null,
        isContentOverflowing: false,
        scrollButtonWidth: 44,
        windowWidth: window.innerWidth,
      };
    },
    computed: {
      quickFiltersStyle() {
        const width = this.windowWidth;
        let baseVW = 100;

        if (width >= 2100) {
          baseVW = 30;
        } else if (width >= 1900) {
          baseVW = 40;
        } else if (width >= 1675) {
          baseVW = 45;
        } else if (width >= 1450) {
          baseVW = 35;
        } else if (width >= 1300) {
          baseVW = 70;
        } else if (width >= 1200) {
          baseVW = 80;
        } else {
          baseVW = 50;
        }

        return {
          maxWidth: `calc(${baseVW}vw - 2rem)`,
        };
      },
      quickViewNames() {
        return this.quickViewOptions.map(({ name }) => name);
      },
      activeViewsCount() {
        return this.quickViewOptions.filter( o => !!o.active).length;
      },
      arrowDirection() {
        return this.showCondition ? "nulodgicon-chevron-up scroll-up" : "nulodgicon-chevron-down";
      },
      computedOptions() {
        const result = [
          ...(this.allowCustomAdditions ? [{ name: this.customTicketViewLabel }] : []), 
          ...this.quickViewOptions,
        ];

        return result;
      },
      isVerticalStyle() {
        return this.displayStyle === 'vertical';
      },
      isHorizontalStyle() {
        return this.displayStyle === 'horizontal';
      },
    },
    watch: {
      options(val) {
        this.quickViewOptions = _cloneDeep(this.options);
        if (val.length > 0) {
          this.$nextTick(() => {
            this.checkContentOverflowing();
            this.observeMetricsSize();
          });
        }
      },
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize);
    },
    destroyed() {
      if (this.observer) {
        this.observer.disconnect();
      }
    },
    methods: {
      onWorkspaceChange() {
        window.addEventListener('resize', this.handleResize);
      },
      handleResize() {
        this.windowWidth = window.innerWidth;
      },
      observeMetricsSize() {
        if (this.observer) return;

        const element = document.getElementById('quickViewContainerId');
        this.observer = new ResizeObserver(() => {
          this.checkContentOverflowing();
        });
        this.observer.observe(element);
      },
      checkContentOverflowing() {
        const contentContainer = document.getElementById('quickViewContainerId');
        this.isContentOverflowing = contentContainer.scrollWidth > contentContainer.clientWidth;
        this.$nextTick(() => {
          this.scrollButtonWidth = document.getElementById('rightButtonDiv')?.offsetWidth || 44;
        });
      },
      updateOptions(data) {
        this.$emit('updated-options', data);
      },
      toggleMenu() {
        if (this.isHorizontalStyle) return;

        const moreItems = document.querySelectorAll('.subpage-menu__item.more-items');
        moreItems.forEach(item => {
          item.style.display = item.style.display === 'block' ? 'none' : 'block';
        });

        const icon = document.querySelector('.show-more i');
        if (icon.classList.contains('nulodgicon-chevron-down')) {
          icon.classList.remove('nulodgicon-chevron-down');
          icon.classList.add('nulodgicon-chevron-up');
        } else {
          icon.classList.remove('nulodgicon-chevron-up');
          icon.classList.add('nulodgicon-chevron-down');
        }
      },
      onScroll({ target: { scrollLeft, offsetWidth, scrollWidth }}) {
        const scrollableElement = document.getElementsByClassName("scrollable--hidden-bar")[0];
        const summaryElement = document.getElementsByClassName("templates-width")[0];
        const summaryWidth = summaryElement.offsetWidth;
        const scrollableWidth = scrollableElement.offsetWidth;
        
        this.isScrollable = summaryWidth > scrollableWidth;
        this.showRightScrollButton = (scrollLeft + offsetWidth + this.scrollButtonWidth) < scrollWidth;
        this.showLeftScrollButton = (scrollLeft > 1);
      },
      scrollTo(side) {
        const scrollableElement = document.getElementsByClassName('scrollable--hidden-bar')[0];
        let scrollValue = 0;
        if (side === 'right') {
          scrollValue = scrollableElement.scrollLeft + 250;
        } else {
          scrollValue = scrollableElement.scrollLeft - 250;
        }
        if (scrollableElement) {
          scrollableElement.scrollTo({
            left: scrollValue,
            behavior: 'smooth',
          });
        }
      },
      scrollLeft() {
        const scrollableElement = document.getElementsByClassName('scrollable--hidden-bar')[0];
        if (scrollableElement) {
          scrollableElement.scrollTo({
            left: scrollableElement.scrollLeft - 250,
            behavior: 'smooth',
          });
        }
      },
      openFiltersListModal() {
        this.$refs.quickFiltersList.open();
      },
      openFilterModal(data = {}) {
        this.$refs.quickViewFilterForm.reset();
        if (data.filters) {
          this.$refs.quickViewFilterForm.open(data, true);
        } else {
          this.$refs.quickViewFilterForm.open();
        }
      },
      toggleShow() {
        this.showCondition = !this.showCondition;
      },
      isActive(quickFilter) {
        const isCustomView = this.allowCustomAdditions && !this.activeViewsCount && quickFilter.name === this.customTicketViewLabel;
        return quickFilter.active || isCustomView;
      },
      quickFilterClicked(quickFilter) {
        const value = quickFilter.name;
        if (!value) {
          return;
        }

        this.$emit('input', { quickFilterName: value, data: quickFilter });
      },
      showAddNewViewButton(quickFilter) {
        return this.allowCustomAdditions && (quickFilter.name === this.customTicketViewLabel);
      },
      addNewViewButtonActive(quickFilter) {
        return this.allowCustomAdditions && !this.activeViewsCount && (quickFilter.name === this.customTicketViewLabel);
      },
    },
  };
</script>
<style scoped lang="scss">
  .nulodgicon-android-add-circle {
    font-size: 1.125rem;
  }

  .right-button-css {
    left: -28px;
    top: -3px;
  }

  .right-top-button-css {
    top: -1.125rem
  }

  .left-button-css {
    right: 3px;
    top: -20px
  }

  .custom-border:focus {
    outline: none
  }

  .show-more {
    cursor: pointer;
    position: relative;
    left: 42%;
  }

  .submenu_conditional {
    .subpage-menu__item {
      display: block;
    }
  }

  .subpage-menu__item {
    font-size: 0.8125rem;
    letter-spacing: 0.1px;
    padding: 0 0.375rem;

    &:hover {
      background-color: rgba(255,255,255,0.35);
    }
  }

  .subpage-menu__item.more-items {
    display: none;
  }

  .subpage-menu {
    max-width: 75%;
  }

  .scrollable--hidden-bar {
    padding-top: 0;
    :deep(.scroll-btn-gradient) {
      width: 5rem !important;
    }
  }
</style>
