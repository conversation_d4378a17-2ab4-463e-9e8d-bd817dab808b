<template>
  <div>
    <div class="flex-column d-flex box text-center pb-5">
      <integration-progress-bar
        v-if="isPending"
        :value="loadingValue"
        class="position-absolute w-100 top-0 left-0"
      />

      <span
        v-else-if="(isSuccess && !isDeactivated) || isAnyChannelConnected"
        class="status-badge success"
      >
        Connected
      </span>

      <span
        v-else-if="isFailed"
        v-tooltip="tooltipErrorMsg"
        class="status-badge danger"
      >
        Sync Failed
      </span>

      <img
        :src="addItemOption.imgPath"
        height="75"
        class="connector-img mb-3"
      >
      <p class="font-weight-bold mb-1">
        {{ addItemOption.name }}
        <span
          v-if="addItemOption.subName"
          class="not-as-small text-muted font-weight-normal"
        >
          ({{ addItemOption.subName }})
        </span>
        <span
          v-if="isBeta"
          class="beta-badge-connector"
        >
          Beta
        </span>
      </p>
      <p
        class="not-as-small text-muted "
        :class="{'pb-3': !canManage}"
      >
        {{ addItemOption.blurb }}
      </p>
      <a
        v-if="addItemOption.helpCenterLink"
        v-tooltip="'Help Center'"
        :href="`${addItemOption.helpCenterLink}`"
        type="button"
        class="text-very-muted position-absolute ml-1 help-center-link"
        target="_blank"
      >
        <i class="genuicon-info-circled align-middle h5" />
      </a>

      <a
        v-if="isSuccess && integratedVendorsCount > 0"
        v-tooltip="`${integratedVendorsCount} new vendors fetched`"
        class="text-very-muted position-absolute bell-icon"
        @click="openCustomize"
      >
        <i class="genuicon-alerts align-middle h5" />
      </a>

      <div
        v-if="showLastSync && isNotSlackOrTeams"
        class="badge-light left-0 mt-1 p-1 position-absolute smallest text-muted box-badge--sync-at"
      >
        Last synced: {{ syncDate }}
      </div>

      <div
        v-if="canManage && isThirdPartyConnector"
        class="mt-auto"
      >
        <router-link
          v-if="isHelpDeskIntegration"
          :to="intgDetail.link"
          :class="{ disabled: !isWrite }"
          class="btn btn-outline-primary btn-sm"
        >
          {{ buttonText }}
        </router-link>
        <button
          v-else-if="isDeactivated"
          class="btn btn-outline-primary btn-sm"
          :disabled="isPending"
          @click.stop.prevent="openSyncModal"
        >
          {{ syncButtonState }}
        </button>
        <button
          v-else-if="isComplete && !isPlaidIntegration"
          v-tooltip="syncButtonTooltip"
          class="btn btn-outline-light btn-sm text-muted"
          @click.stop.prevent="openDeleteModal"
        >
          Disable {{ syncButtonText }}
        </button>
        <button
          v-else-if="isComplete && isPlaidIntegration"
          class="btn btn-outline-primary btn-sm"
          @click.stop.prevent="openCustomize"
        >
          Manage accounts
        </button>
        <button
          v-else
          class="btn btn-outline-primary btn-sm"
          :disabled="isPending"
          @click.stop.prevent="openSyncModal"
        >
          {{ syncButtonState }}
        </button>

        <button
          v-if="canManage && isCustomizable && isComplete && intgDetail.active && !isPlaidIntegration"
          v-tooltip="'Customize'"
          class="btn btn-outline-light btn-sm text-muted"
          @click="openCustomize"
        >
          <i class="nulodgicon-edit" />
        </button>
        <button
          v-if="canManage && isComplete && !isPlaidIntegration && showConnectorsDetails"
          v-tooltip="'Connector Details'"
          class="btn btn-outline-light btn-sm text-muted"
          @click="openDetailsModal"
        >
          <i class="nulodgicon-eye" />
        </button>
      </div>

      <button
        v-else-if="canManage"
        class="btn btn-outline-primary btn-sm mt-auto"
        @click.stop.prevent="openSyncModal"
      >
        Add {{ modulePrefix.replace('managed_','') }}
      </button>
    </div>
  </div>
</template>

<script>
  import IntegrationProgressBar from 'components/shared/module_onboarding/integration_progress_bar.vue';
  import common from './common';

  // currently no beta integrations exist
  // previously were: 'Microsoft Intune', 'Azure AD Devices', 'Kaseya', 'Kandji', 'Jamf Pro', 'Slack', 'Microsft Teams'

  const BETA_INTEGRATIONS = [];

  export default {
    components: {
      IntegrationProgressBar,
    },
    mixins: [common],
    props: {
      addItemOption: {
        type: Object,
        required: true,
      },
      intgDetail: {
        required: true,
      },
      loadingValue: {
        required: true,
      },
      isCustomizable: {
        type: Boolean,
        default: false,
      },
      isVendorIntegration: {
        type: Boolean,
        default: false,
      },
      integratedVendorsCount: {
        type: Number,
        default: 0,
      },
    },
    computed: {
      syncDate() {
        const date = this.intgDetail.lastSyncedAt;
        if (date) { 
          return moment(date.toString()).format('MM-DD-YYYY');
        }
        return '';
      },
      isThirdPartyConnector() {
        return this.addItemOption.thirdPartyConnector;
      },
      isComplete() {
        return this.isSuccess || this.isFailed;
      },
      isPlaidIntegration() {
        return this.intgDetail.name === "plaid";
      },
      isBeta() {
        return BETA_INTEGRATIONS.includes(this.addItemOption.name);
      },
      syncButtonState() {
        return this.isPending ? 'Syncing...' : 'Sync Account';
      },
      showLastSync() {
        return this.intgDetail && !["Discovery Agent", "Network Probe", "Self-Onboarding"].includes(this.addItemOption.name);
      },
      syncButtonText() {
        return this.isVendorIntegration ? '' : '/ Resync';
      },
      syncButtonTooltip() {
        return this.isVendorIntegration ? 'Disable or Delete' : 'Disable or Delete or Resync';
      },
      isNotSlackOrTeams() {
        const options = ["Slack", "Microsoft Teams"];
        return !options.includes(this.addItemOption.name);
      },
      buttonText() {
        return this.isAnyChannelConnected ? 'Show Connections' : 'Connect Channel';
      },
      showConnectorsDetails() {
        const intgs = ['meraki', 'kaseya', 'ubiquiti', 'kandji', 'mosyle'];
        return intgs.includes(this.intgDetail.name);
      },
    },
    methods: {
      openSyncModal() {
        this.$emit('open-sync-modal', this.addItemOption.openModalStr);
      },
      openDeleteModal() {
        this.$emit('open-delete-modal', this.intgDetail);
      },
      openCustomize() {
        this.$emit(`customize-integration`, this.addItemOption.openModalStr);
      },
      openDetailsModal() {
        this.$emit('show-integration-details', this.intgDetail);
      },
    },
  };
</script>

<style lang="scss" scoped>
.help-center-link {
  bottom: 0;
  right: 4px;
}
.bell-icon {
  bottom: 0;
  right: 1.875rem;
  color: $color-accent !important;
}
.status-badge {
  color: white;
  display: inline-block;
  font-size: 11px;
  font-weight: 700;
  height: 16px;
  line-height: 8px;
  padding: 4px 6px;
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-left-radius: 5px;

  &.success {
    background: $pastel-green-100;
  }

  &.danger {
    background: $color-danger;
  }
}
.beta-badge-connector {
  background-color: #ff8201;
  display: inline-block;
  border-radius: 2rem;
  font-size: 12px;
  color: white;
  width: 35px;
}

.connector-img {
  border-radius: .25rem;
}
</style>
