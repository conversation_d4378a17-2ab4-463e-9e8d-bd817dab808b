<template>
  <div>
    <div class="calendar">
      <div class="calendar-grid">
        <div
          v-for="(day, index) in calendarDays"
          :key="index"
          class="calendar-day"
          :class="{
            'disabled-day': day.isOtherMonth,
            'selected-day': selectedDate === day.fullDate && !day.isOtherMonth,
          }"
          @click="selectDate(day)"
        >
          <div class="date-info">
            <span class="date-number">{{ day.date }}</span>
            <span class="day-name">{{ day.day }}</span>
          </div>
          <div
            v-if="getVendorsForDay(day.fullDate).length"
            class="vendors"
          >
            <div class="vendor-item">
              <icon-link
                v-if="getVendorsForDay(day.fullDate)[0]"
                class="mr-2"
                calendar-format
                :name="getVendorsForDay(day.fullDate)[0].vendor.name"
                :url="getVendorsForDay(day.fullDate)[0].url"
                :size="40"
              />
              <span
                v-if="getVendorsForDay(day.fullDate)[0]"
                class="annual-cost"
              >
                ${{ formatToUnits(getVendorsForDay(day.fullDate)[0].annualCost, 2) }} / year
              </span>
            </div>
            <div
              v-if="getVendorsForDay(day.fullDate).length > 1"
              class="contract-count clickable--with-hover rounded p-1"
              @click="openContractModal(day.fullDate)"
            >
              <span class="contract-count">
                1 / {{ getVendorsForDay(day.fullDate).length }}
              </span>
              <i class="nulodgicon-chevron-right float-right cursor-pointer"/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sweet-modal 
      ref="contractModal"
      v-sweet-esc
      class="mt-5"
      :title="formattedModalDate"
    >
      <table class="sweet-modal-table">
        <thead>
          <tr>
            <th>Contract Name</th>
            <th>Vendor Name</th>
            <th>End Date</th>
            <th>Annual Cost</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="contract in modalContracts"
            :key="contract.id"
          >
            <td @click="routeContract(contract)">{{ contract.name }}</td>
            <td @click="routeContract(contract)">{{ contract.vendor.name }}</td>
            <td @click="routeContract(contract)">{{ contract.endDate }}</td>
            <td @click="routeContract(contract)">${{ contract.annualCost.toLocaleString() }}</td>
          </tr>
        </tbody>
      </table>
    </sweet-modal>
  </div>
</template>

<script>
  import IconLink from 'components/shared/icon_link';
  import { SweetModal } from 'sweet-modal-vue';
  import calendarHelper from '../../mixins/calendar_helper';
  import currency  from '../../mixins/currency';

  export default {
    components: {
      IconLink,
      SweetModal,
    },
    mixins: [calendarHelper, currency],
    props: {
      currentMonth: {
        type: Number,
        required: true,
      },
      currentYear: {
        type: Number,
        required: true,
      },
      contractsData: {
        type: Object,
        default: () => ({}),
      },
      vendorsData: {
        type: Object,
        default: () => ({}),
      },
      isMonthlyContract: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        selectedDate: null,
        modalContracts: [],
        formattedModalDate: "",
      };
    },
    computed: {
      monthName() {
        return new Date(this.currentYear, this.currentMonth).toLocaleString("default", { month: "long" });
      },
      calendarDays() {
        return this.generateCalendarDays();
      },
    },
    watch: {
      currentMonth() {
        this.selectedDate = null;
      },
    },
    methods: {
      routeContract(contract) {
        this.$router.push({ path: `/${contract.id}` });
      },
      getDayName(year, month, date) {
        return new Date(year, month, date).toLocaleString("default", { weekday: "short" }).toUpperCase();
      },
      selectDate(day) {
        if (!day.isOtherMonth) {
          const newDate = this.selectedDate === day.fullDate ? null : day.fullDate;
          this.selectedDate = newDate;
          this.$emit("dateSelected", newDate);
        }
      },
      getVendorsForDay(fullDate) {
        const monthKey = this.months[this.currentMonth].toLowerCase();
        if (!this.contractsData[monthKey]) return [];

        const formattedDate = fullDate.replace(/-(\d{1})-/g, "-0$1-").replace(/-(\d{1})$/, "-0$1");
        const dateField = this.isMonthlyContract ? 'startDate' : 'endDate';

        return this.contractsData[monthKey]
          .filter(contract => contract[dateField] === formattedDate)
          .map(contract => ({
            ...contract,
            vendor: { ...contract.vendor },
            annualCost: contract.monthlyCost ? contract.monthlyCost * 12 : 0,
          }));
      },
      openContractModal(fullDate) {
        const dateObj = new Date(fullDate);
        this.formattedModalDate = dateObj.toLocaleDateString("en-US", {
          year: "numeric", month: "long", day: "numeric",
        });
        this.modalContracts = this.getVendorsForDay(fullDate);
        this.$refs.contractModal.open();
      },
      generateCalendarDays() {
        const firstDay = new Date(this.currentYear, this.currentMonth, 1);
        const lastDay = new Date(this.currentYear, this.currentMonth + 1, 0);
        const firstWeekday = firstDay.getDay() === 0 ? 6 : firstDay.getDay() - 1;

        const prevMonthLastDate = new Date(this.currentYear, this.currentMonth, 0).getDate();
        const prevMonthYear = this.currentMonth === 0 ? this.currentYear - 1 : this.currentYear;
        const prevMonth = this.currentMonth === 0 ? 11 : this.currentMonth - 1;
        const nextMonthYear = this.currentMonth === 11 ? this.currentYear + 1 : this.currentYear;
        const nextMonth = this.currentMonth === 11 ? 0 : this.currentMonth + 1;

        const days = [];
        for (let i = firstWeekday - 1; i >= 0; i -= 1) {
          days.push(this.createDay(prevMonthYear, prevMonth, prevMonthLastDate - i, true));
        }

        for (let date = 1; date <= lastDay.getDate(); date += 1) {
          days.push(this.createDay(this.currentYear, this.currentMonth, date, false));
        }

        const remainingDays = 7 - (days.length % 7);
        if (remainingDays < 7) {
          for (let i = 1; i <= remainingDays; i += 1) {
            days.push(this.createDay(nextMonthYear, nextMonth, i, true));
          }
        }
        return days;
      },
      createDay(year, month, date, isOtherMonth) {
        return {
          date,
          day: this.getDayName(year, month, date),
          isOtherMonth,
          fullDate: `${year}-${month + 1}-${date}`,
        };
      },
    },
  };
</script>

<style lang="scss" scoped>
  .calendar {
    margin: auto;
    background: var(--themed-light) !important;
    border-radius: 0.625rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.1);
  }

  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.375rem;
  }

  .calendar-day {
    background: var(--themed-box-bg);
    min-height: 8.25rem;
    padding: 0.625rem;
    border-radius: 0.5rem;
    border: 0.063rem solid #ddd;
    font-size: 1rem;
    font-weight: bold;
    height: auto;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .disabled-day {
    background-color: var(--themed-very-fair);
    color: var(--themed-muted);
  }

  .selected-day {
    background-color: var(--themed-box-bg) !important;
    border: 0.125rem solid $primary;
  }

  .date-info {
    display: flex;
    justify-content: space-between;
  }

  .date-number {
    font-size: 1.2rem;
    font-weight: bold;
  }

  .day-name {
    font-size: 0.9rem;
    color: var(--themed-muted);
  }

  .vendor-item {
    display: flex;
    align-items: center;
    gap: 0.313rem;
    margin-top: 0.625rem;
  }

  .annual-cost,
  .contract-count {
    font-size: 1rem;
  }

  .contract-count {
    justify-content: space-between;
    color: var(--themed-muted);
    margin-top: 0.5rem;
  }

  .sweet-modal .sweet-title {
    font-size: 1.25rem;
    font-weight: bold;
  }

  .sweet-modal-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.625rem;
  }

  .sweet-modal-table th,
  .sweet-modal-table td {
    padding: 0.625rem;
    text-align: left;
    border: 0.063rem solid #ddd;
  }

  .sweet-modal-table th {
    background-color: var(--themed-light);
    font-weight: bold;
  }

  .sweet-modal-table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
  }

  .sweet-modal-table tbody tr:hover {
    background-color: var(--themed-light);
  }
</style>
