<template>
  <div class="clearfix row">
    <div class="col">
      <div class="box box--with-heading box--flat">
        <div class="box__heading pt-3 pb-3 rounded-top d-flex justify-content-between">
          <div class="d-flex align-items-center">
            <div class="d-flex align-items-center date-header ml-2 mr-5">
              <span
                v-if="!isUpcomingView"
                class="cursor-pointer theme mr-2"
                @click="isMonthView ? changeMonth(-1) : changeYear(-1)"
              >
                <i class="nulodgicon-chevron-left text-very-muted" />
              </span>
              <h4 
                v-if="!isUpcomingView" 
                class="mb-0 font-weight-bold"
              >{{ isMonthView ? months[selectedMonth] + " " + currentYear : currentYear }}</h4>
              <h5
                v-else
                class="mb-0 font-weight-bold"
              >
                {{ currentYear + " - " + (currentYear + 1) }}
              </h5>
              <span
                v-if="!isUpcomingView"
                class="cursor-pointer theme ml-2"
                @click="isMonthView ? changeMonth(1) : changeYear(1)"
              >
                <i class="nulodgicon-chevron-right text-very-muted" />
              </span>
            </div>
            <div class="form-group mb-0">
              <select
                id="view-select"
                v-model="selectedView"
                class="calendar-view custom-select border-0 not-as-small h-auto ny-n1 btn-faded-light"
                :style="{ width: selectedView === 'Month' ? '4rem' : '4.75rem' }"
              >
                <option
                  v-for="view in availableViews"
                  :key="view"
                  :value="view"
                >
                  {{ view }}
                </option>
              </select>
            </div>
          </div>
          <div class="text-right mt-4 mt-lg-0 ml-4 d-flex align-items-center">
            <filter-button
              :active-filters-count="activeCalendarFiltersCount"
              is-header-variant
              @toggle-filter="toggleFilterMenu"
            />
            <div class="form-group mb-0 ml-3">
              <div class="btn-group btn-group-toggle">
                <label
                  v-tooltip.bottom="'Open-ended or non-existent term'"
                  class="radio-btn btn btn-sm btn-faded-light"
                  :class="{
                    'active btn-outline-not-as-light': contractType === 'fixed_term'
                  }"
                >
                  <input
                    v-model="contractType"
                    type="radio"
                    name="contract[contract_type]"
                    value="fixed_term"
                    @input="onTypeChange('fixed_term')"
                  >
                  Fixed Term Contracts
                </label>
                <label
                  v-tooltip.bottom="'Agreement with a fixed term'"
                  class="radio-btn btn btn-sm btn-faded-light"
                  :class="{
                    'active btn-outline-not-as-light': contractType === 'open_ended'
                  }"
                >
                  <input
                    v-model="contractType"
                    type="radio"
                    name="contract[contract_type]"
                    value="open_ended"
                    @input="onTypeChange('open_ended')"
                  >
                  Monthly Contracts
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex pt-3 w-100 bg-themed-light">
          <div class="col-3 ml-2">
            <div class="search-wrap">
              <input
                ref="searchInput"
                v-model="internalSearch"
                type="text"
                class="form-control search-input"
                placeholder="Search your contracts by name or vendor..."
              >
              <i
                class="nulodgicon-ios-search-strong search-input-icon"
                @click.prevent="$refs.searchInput.focus"
              />
            </div>
          </div>

          <div class="pl-3 ml-n2 contacts-bar col-2">
            <contributors-select
              ref="contributorSelect"
              name="contract"
              placeholder="Filter by contacts"
              allow-empty
              multiple
              is-calendar
              :allow-multiple="true"
              show-tags
              :permissions-options="contacts"
              :value="selectedContacts"
              @select="$emit('select-contact', $event)"
              @remove="$emit('remove-contact', $event)"
            />
          </div>

          <div
            v-if="displayClearButton"
            class="reset-btn cursor-pointer"
            @click="resetFilters"
          >
            <div 
              class="btn btn-xs btn-link no-shadow true-small text-red-dark border-0 clear-btn"
            > Clear All </div>
          </div>
        </div>
        <div 
          v-if="activeCalendarFilters.length"
          class="bg-themed-light d-flex flex-row-reverse pr-2 w-100"
          :class="{ 'pt-6': showFilters }"
        >
          <span
            v-for="filter in activeCalendarFilters"
            :key="`${filter.filterName}_${filter.filter.id}`"
            class="align-self-center mr-2"
          >
            <active-filter
              :filter="filter"
              @clear-filter="clearFilter"
            />
          </span>
        </div>
        <div class="bg-themed-light w-100 pt-4 px-4 pt-lg-2 pt-xl-3">
          <hr class="pb-2 pt-2 w-100 bg-themed-light m-0 mr-3">
          <div
            v-if="!isMonthView"
            class="box__inner bg-themed-light calendar-grid"
          >
            <div
              v-for="(month, index) in calendarMonths"
              :key="index"
              class="month-box"
              :class="{ 
                'selected-month': isMonthSelected(index), 
                'position-relative': getAlertsForMonth(month).length >= 1 
              }"
              @click="handleMonthClick(index)"
            >
              <div 
                v-if="getAlertsForMonth(month).length >= 1"
                class="rounded-circle bg-yellow d-flex justify-content-center align-items-center position-absolute"
                :style="{
                  width: '1.5rem',
                  height: '1.5rem',
                  right: '0.5rem',
                  top: '0.5rem',
                  boxShadow: `0 2px 2px 0 rgba(0, 0, 0, 0.14),
                              0 3px 1px -2px rgba(0, 0, 0, 0.12),
                              0 1px 5px 0 rgba(0, 0, 0, 0.2)`
                }"
                @click.prevent.stop="openAlertContractsModal(month)"
              >
                <i class="genuicon-alerts"/>
              </div>
              <div class="h5 mb-0.5 month-name mt-2">{{ monthName(month) }}</div>
              <div class="d-flex align-items-center mb-3">
                <span 
                  class="contract-count not-as-small ml-0"
                  :class="contractRenewals[index] !== 'No renewals' ? 'text-secondary' : 'text-very-muted'"
                >
                  {{ contractRenewals[index] }}
                </span>
                <span class="mx-2 smallest text-very-muted">•</span>
                <span
                  v-if="getAlertsForMonth(month).length"
                  class="text-secondary not-as-small"
                >{{getAlertsForMonth(month).length }} Alerts
                </span>
                <span v-else>
                  <span class="text-very-muted not-as-small">No Alerts</span>
                </span>
              </div>
              <div class="d-flex align-items-center mt-1">
                <img
                  src="https://cdn-icons-png.flaticon.com/512/3587/3587880.png"
                  height="24"
                  style="opacity: 0.35"
                >
                <span
                  v-if="totalAnnualCost[month.toLowerCase()]"
                  class="contract-count"
                >
                  {{ totalAnnualCost[month.toLowerCase()] }}
                </span>
                <span v-else>
                  <span class="contract-count">-</span>
                </span>
                <span
                  v-if="percentageChanges[month.toLowerCase()] !== null"
                  :class="[
                    'rounded-pill px-1 smallest ml-2',
                    percentageChanges[month.toLowerCase()] >= 0 ? 'bg-red-subtle text-red' : 'bg-green-subtle text-green'
                  ]"
                >
                  <i
                    class="nulodgicon-arrow-left-c mr-n0.5 d-inline-block"
                    :class="percentageChanges[month.toLowerCase()] >= 0 ? 'rotate-up' : 'rotate-down'"
                  />
                  {{ Math.abs(percentageChanges[month.toLowerCase()]) }}%
                </span>
              </div>
              <div 
                v-if="getVendorsForMonth(month).length"
                class="smallest text-muted mt-3"
              >Added & removed Contracts</div>
              <div class="d-flex">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  version="1.1"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  width="512"
                  height="512"
                  x="0"
                  y="0"
                  viewBox="0 0 24 24"
                  style="width: 1.5rem; height: 1.5rem; opacity: 0.35; margin-top: 0.75rem"
                  xml:space="preserve"
                  class="mr-3"
                >
                  <g>
                    <g 
                      fill="#000" 
                      fill-rule="evenodd" 
                      clip-rule="evenodd"
                    >
                      <path
                        d="M16.335 3.33a.75.75 0 0 1 .336 1.005l-8 16a.75.75 0 1 1-1.342-.67l8-16a.75.75 0 0 1 1.006-.336zM7 3.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V4A.75.75 0 0 1 7 3.25z"
                        fill="#000000"
                        opacity="1"
                        data-original="#000000"
                        class=""
                      />
                      <path
                        d="M3.25 7A.75.75 0 0 1 4 6.25h6a.75.75 0 0 1 0 1.5H4A.75.75 0 0 1 3.25 7zM13.25 17a.75.75 0 0 1 .75-.75h6a.75.75 0 0 1 0 1.5h-6a.75.75 0 0 1-.75-.75z"
                        fill="#000000"
                        opacity="1"
                        data-original="#000000"
                        class=""
                      />
                    </g>
                  </g>
                </svg>
                <div
                  v-if="getVendorsForMonth(month).length"
                  class="d-flex mt-2 mr-2"
                >
                  <span
                    v-for="(vendor, vendorIndex) in getVendorsForMonth(month).slice(0, 7)"
                    :key="vendorIndex"
                  >
                    <icon-link
                      class="mr-2"
                      calendar-format
                      contract-calendar
                      :contract-type="contractType === 'fixed_term'"
                      :name="vendor.name"
                      :url="vendor.url"
                      :size="30"
                    />
                  </span>
                  <span
                    v-if="getVendorsForMonth(month).length > 5"
                    v-tooltip="getAdditionalVendorNames(month)"
                    class="more-vendors"
                  >
                    <span  class="extra-icon">
                      +{{ getVendorsForMonth(month).length - 7 }}
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div
            v-else
            class="w-100 d-light p-0"
          >
            <calendar-day-view
              :current-month="monthView"
              :current-year="currentYear"
              :contracts-data="contracts"
              :vendors-data="vendors"
              :is-monthly-contract="contractType === 'open_ended'"
              @dateSelected="handleDateSelected"
            />
          </div>
          <div class="col-12 col-xxl-4 mt-xxl-0 p-0">
            <div class="card-background contract-background border-0">
              <div class="d-flex justify-content-between mt-4">
                <div class="d-flex">
                  <span 
                    v-if="contractsNumber"
                    class="number-of-contracts d-flex align-items-center"
                  >
                    <h4 
                      v-if="!isUpcomingView"
                      class="m-0"
                    >{{ isMonthView ? months[selectedMonth] + " " + currentYear : currentYear }}</h4>
                    <h5
                      v-else
                      class="m-0"
                    >
                      {{ currentYear + " - " + (currentYear + 1) }}
                    </h5>
                    <span class="ml-2"> ({{ contractsForCurrentView.length }} Contracts Renewing) </span>
                  </span>
                </div>
                <div class="float-right">
                  <div class="d-inline-block position-relative">
                    <button
                      v-if="isReadAny || isWrite"
                      v-tooltip.top="'Export Contract Data'"
                      class="btn btn-link text-secondary export-button"
                      @click="exportContractData"
                    >
                      <i class="nulodgicon-cloud-download mr-1" />
                      Export
                    </button>
                  </div>
                </div>
              </div>
              <div class="p-0 my-1 mx-0">
                <toggle-view
                  :key="contractsNumber"
                  :view="'list'"
                  :table-type="'scrollable'"
                  :min-width="tableMinWidth(headers.length)"
                  :is-loading="loadingStatus"
                  can-reset-widths
                  is-contracts-calendar
                >
                  <template slot="list">
                    <thead>
                      <table-header-row
                        :table-header="tableHeader"
                        :sort-item="sortItem"
                        :active-sort="activeSort"
                        :active-sort-direction="activeSortDirection"
                        @change="setSorting"
                      />
                    </thead>
                    <tbody>
                      <contract-list-item
                        v-for="contract in contractsForCurrentView"
                        :key="contract.id"
                        :contract="contract"
                        :contract-preferences="tableHeader"
                        is-contracts-calendar
                        class="box--with-hover list"
                      />
                    </tbody>
                  </template>
                </toggle-view>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <sweet-modal 
      ref="contractAlertModal"
      v-sweet-esc
      class="mt-5"
      :title="formattedModalDate"
    >
      <table class="sweet-modal-table">
        <thead>
          <tr>
            <th>Contract Name</th>
            <th>Vendor Name</th>
            <th>Alert Date</th>
            <th>Annual Cost</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="contract in modalContracts"
            :key="contract.id"
          >
            <td 
              class="cursor-pointer text-blue-600 hover:underline"
              @click="routeContract(contract)" 
            >{{ contract.name }}</td>
            <td 
              class="cursor-pointer"
              @click="routeContract(contract)" 
            >{{ contract.vendor }}</td>
            <td 
              class="cursor-pointer"
              @click="routeContract(contract)" 
            >{{ contract.date }}</td>
            <td 
              class="cursor-pointer"
              @click="routeContract(contract)" 
            >${{ contract.annualCost }}</td>
          </tr>
        </tbody>
      </table>
    </sweet-modal>
  </div>
</template>

<script>
  import IconLink from 'components/shared/icon_link';
  import { mapGetters } from 'vuex';
  import activeSortHelper from 'mixins/active_sort_helper';
  import permissionsHelper from 'mixins/permissions_helper';
  import tableLayoutStyle from 'mixins/table_layout_style';
  import sortingHelper from "mixins/sorting_helper";
  import { SweetModal } from 'sweet-modal-vue';
  import CalendarDayView from './calendar-day-view.vue';
  import calendarHelper from '../../mixins/calendar_helper';
  import currency  from '../../mixins/currency';
  import FilterButton from "../shared/filter_button.vue";
  import ContributorsSelect from '../shared/contributors_select.vue';
  import ContractListItem from './contract_list_item.vue';
  import TableHeaderRow from '../shared/list_view_table_header.vue';
  import ToggleView from '../shared/toggle_view.vue';
  import ActiveFilter from '../shared/active_filter.vue';

  export default {
    components: { 
      IconLink, 
      CalendarDayView,
      FilterButton,
      ContributorsSelect,
      ContractListItem,
      TableHeaderRow,
      ToggleView,
      ActiveFilter,
      SweetModal,
     },
    mixins: [calendarHelper, currency, activeSortHelper, permissionsHelper, tableLayoutStyle, sortingHelper],
    props: {
      availableViews: {
        type: Array,
        default: () => ["Calendar Year", "Upcoming 12 Months", "Month"],
      },
      defaultView: {
        type: String,
        default: "Calendar Year",
      },
      contracts: {
        type: Object,
        default: () => ({}),
      },
      vendors: {
        type: Object,
        default: () => ({}),
      },
      search: {
        type: String,
        default: "",
      },
      contacts: {
        type: Array,
        default: () => [],
      },
      selectedContacts: {
        type: Array,
        default: () => [],
      },
      contractsNumber: {
        type: Number,
        default: 0,
      },
      pagedContracts: {
        type: Array,
        default: () => [],
      },
      alerts: {
        type: Object,
        default: () => ({}),
      },
    },
    emits: ['update:search', 'select-contact', 'remove-contact'],
    data() {
      const today = new Date();
      return {
        currentYear: today.getFullYear(),
        currentMonth: today.getMonth(),
        selectedView: this.defaultView,
        selectedDate: null,
        selectedMonth: null,
        clickTimeout: null,
        internalSearch: this.search || '',
        contractType: 'open_ended',
        formattedModalDate: '',
        headers: [
          { title: 'End Date', sortBy: 'end_date', name: 'end_date' },
          { title: 'Vendor', sortBy: 'vendor_name', name: 'vendor_name' },
          { title: 'Contract Name', sortBy: 'contract_name', name: 'name' },
          { title: 'Annual / Monthly Cost', sortBy: 'cost', name: 'cost' },
          { title: 'Contract Term', sortBy: 'contract_term', name: 'contract_term' },
          { title: 'Contract Contacts', sortBy: 'contract_alert_recipients', name: 'contacts' },
        ],
        modalContracts: [],
        showFilters: false,
      };
    },
    computed: {
      ...mapGetters({
        activeCalendarFiltersCount: "activeCalendarFiltersCount",
        activeCalendarFilters: "activeCalendarFilters",
        loadingStatus: "loadingStatus",
      }),
      tableHeader() {
        return this.headers;
      },
      isMonthView() {
        return this.selectedView === "Month";
      },
      isUpcomingView() {
        return this.selectedView === 'Upcoming 12 Months';
      },
      monthView() {
        return this.selectedMonth !== null ? this.selectedMonth : this.currentMonth;
      },
      contractRenewals() {
        return this.calendarMonths.map((month) => {
          const monthKey = month.toLowerCase();
          return this.contracts[monthKey]?.length > 0
            ? `${this.contracts[monthKey].length} renewals`
            : "No renewals";
        });
      },
      getVendorsForMonth() {
        return (month) => this.vendors[month.toLowerCase()] || [];
      },
      getAlertsForMonth() {
        return (month) => this.alerts[month.toLowerCase()] || [];
      },
      contractsForCurrentView() {
        if (this.isUpcomingView) {
          return this.pagedContracts;
        }

        const isFixedTerm = this.contractType === 'fixed_term';
        const dateField = isFixedTerm ? 'endDate' : 'startDate';

        return this.pagedContracts?.filter(contract =>
          contract[dateField]?.startsWith(this.currentYear.toString())
        );
      },
      totalAnnualCost() {
        const costByMonth = {};
        const previousMonthKey = this.isUpcomingView ? this.months[this.currentMonth].toLowerCase() : `december${this.currentYear - 1}`;
        
        const monthContracts = this.contracts[previousMonthKey] || [];
        const monthTotal = monthContracts.reduce((sum, contract) => sum + (contract.monthlyCost * 12), 0);
        const monthAmount = this.formatToUnits(monthTotal, 2);
        costByMonth[previousMonthKey] = monthTotal > 0 ? monthAmount : null;
        
        this.calendarMonths.forEach((month) => {
          const key = month.toLowerCase();
          const contracts = this.contracts[key] || [];
          const total = contracts.reduce((sum, contract) => sum + (contract.monthlyCost * 12), 0);
          const amount = this.formatToUnits(total, 2);
          costByMonth[key] = total > 0 ? amount : null;
        });
        return costByMonth;
      },
      percentageChanges() {
        const changes = {};
        const months = Object.keys(this.totalAnnualCost);

        months.forEach((month, index) => {
          if (index === 0) {
            changes[month] = null;
            return;
          }

          const currentRaw = this.totalAnnualCost[months[index]] || '0';
          const previousRaw = this.totalAnnualCost[months[index - 1]] || '0';

          const current = parseFloat(currentRaw.replace(/,/g, ''));
          const previous = parseFloat(previousRaw.replace(/,/g, ''));

          if (!current || current === 0) {
            changes[month] = null;
          } else if (!previous || previous === 0) {
            changes[month] = 100;
          } else {
            const change = ((current - previous) / previous) * 100;
            changes[month] = parseFloat(change.toFixed(2));
          }
        });
        return changes;
      },
      calendarMonths() {
        const months = [
          "January", "February", "March", "April", "May", "June",
          "July", "August", "September", "October", "November", "December",
        ];

        if (this.isUpcomingView) {
          const keys = [];
          for (let i = 1; i < 13; i+=1) {
            const monthIndex = (this.currentMonth + i) % 12;
            const yearOffset = (this.currentMonth + i) >= 12 ? 1 : 0;
            const year = this.currentYear + yearOffset;
            const key = months[monthIndex] + (yearOffset ? year : "");
            keys.push(key);
          }
          return keys;
        }

        return months;
      },
      displayClearButton() {
        return this.selectedContacts.length || this.internalSearch || this.activeCalendarFilters.length;;
      },
    },
    watch: {
      selectedView(newView) {
        if (newView === 'Calendar Year' || newView === 'Upcoming 12 Months') {
          this.selectedMonth = null;
          if (newView === 'Upcoming 12 Months') {
            this.currentYear = new Date().getFullYear();
          }
        } else if (newView === 'Month' && this.selectedMonth === null) {
          this.selectedMonth = this.currentMonth;
          this.$emit('month-changed', this.selectedMonth);
        }
        this.$emit('view-changed', newView);
      },
      internalSearch(val) {
        this.$emit('update:search', val);
      },
      search(val) {
        this.internalSearch = val;
      },
    },
    methods: {
      changeYear(direction) {
        this.currentYear += direction;
        this.selectedMonth = null;
        this.$emit("year-changed", this.currentYear);
      },
      getAdditionalVendorNames(month) {
        const vendors = this.getVendorsForMonth(month);
        if (vendors.length > 7) {
          return vendors.slice(7).map(vendor => vendor.name).join(", ");
        }
        return "";
      },
      monthName(month) {
        const nextYearStr = (this.currentYear + 1).toString();
        return month.endsWith(nextYearStr)
          ? `${month.slice(0, -nextYearStr.length)} ${nextYearStr}`
          : month;
      },
      changeMonth(direction) {
        const newMonth = this.selectedMonth + direction;
        if (newMonth < 0) {
          this.selectedMonth = 11;
          this.currentYear -= 1;
        } else if (newMonth > 11) {
          this.selectedMonth = 0;
          this.currentYear += 1;
        } else {
          this.selectedMonth = newMonth;
        }
        this.selectedDate = null;
        this.$emit("year-changed", this.currentYear);
        this.$emit("month-changed", this.selectedMonth);
      },
      selectMonth(index) {
        const newMonth = this.selectedMonth === index ? null : index;
        this.selectedMonth = newMonth;
        if (this.isUpcomingView && this.selectedMonth !== null) {
          this.$emit("month-changed", this.calendarMonths[index].toLowerCase());
        } else {
          this.$emit("month-changed", newMonth);
        }
      },
      isMonthSelected(index) {
        return this.selectedMonth === null ? this.contracts[this.calendarMonths[index].toLowerCase()]?.length > 0 : this.selectedMonth === index;
      },
      handleDateSelected(date) {
        this.selectedDate = date;
        this.$emit("date-selected", date);
        this.$emit('view-changed', this.selectedView);
      },
      toggleFilterMenu() {
        this.showFilters = !this.showFilters;
        this.$emit('toggle-filter-menu');
      },
      resetFilters() {
        this.activeCalendarFilters.forEach(filter => {
          this.clearFilter(filter);
        });
        this.$emit('reset-filters');
      },
      clearFilter(filter) {
        this.$emit('clear-filter', filter);
      },
      handleMonthClick(index) {
        if (this.clickTimeout) {
          clearTimeout(this.clickTimeout);
          this.clickTimeout = null;

          this.selectedView = "Month";
          this.$nextTick(() => {
            this.selectedMonth = index;
            this.selectedDate = null;
            this.currentMonth = index;
            this.$emit("month-changed", index);
            this.$emit("view-changed", this.selectedView);
          });
        } else {
          this.clickTimeout = setTimeout(() => {
            this.clickTimeout = null;
            this.selectMonth(index);
          }, 300);
        }
      },
      exportContractData() {
        this.$emit('export');
      },
      setSorting() {
        this.$emit('apply-sorting', this.activeSortDirection);
        this.setSortCookies();
      },
      onTypeChange(contractType) {
        this.contractType = contractType;
        this.$emit('contract-type', contractType);
      },
      openAlertContractsModal(month) {
        const alerts = this.alerts[month.toLowerCase()] || [];

        this.modalContracts = alerts.map(alert => ({
          id: alert.id,
          contractId: alert.contractId,
          name: alert.contractName,
          vendor: alert.vendorName,
          date: alert.date,
          annualCost: alert.annualCost,
        }));

        this.formattedModalDate = `Alerts for ${this.monthName(month)}`;
        this.$refs.contractAlertModal?.open();
      },
      routeContract(contract) {
        this.$router.push({ path: `/${contract.contractId}` });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .date-header {
    font-size: 1.25rem;
  }

  .box__inner {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
  .calendar-grid {
    grid-template-columns: repeat(4, minmax(auto, 1fr)) !important;
    grid-template-rows: repeat(3, minmax(auto, 1fr)) !important;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .month-box {
    height: auto;
    border: 0.0625rem solid #ccc;
    background: var(--themed-box-bg) !important;
    padding-left: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  .selected-month {
    background-color: var(--themed-box-bg) !important;
    border-style: solid;
    border-color: $primary !important;

    border-width: 0.1rem !important;
  }

  .selected-month-full {
    background-color: var(--themed-box-bg) !important;
    border: 0.125rem solid $primary;
  }

  .month-name {
    color: var(--themed-dark);
    font-weight: bold;
  }

  .calendar-view {
    color: var(--themed-box-bg);
    padding-left: 0.375rem;
    border: 0.0625rem solid;
    border-radius: 0.4375rem;
    background-image: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }

  .calendar-view option {
    background-color: white;
    color: black;
    padding: 0.5rem;
    transition: background-color 0.2s ease-in-out;
  }

  .calendar-view option:hover {
    background-color: #f0f0f0;
  }

  .theme {
    background-color: var(--themed-dark-drawer-bg);
    color: var(--themed-box-bg);
  }

  .contract-count {
    display: block;
    font-size: 1.1rem;
    margin-left: 0.8125rem;
  }

  .icon{
    font-size: 1.1rem;
    color: grey;
  }

  .rotate-up {
    transform: rotate(90deg);
  }

  .rotate-down {
    transform: rotate(-90deg);
  }

  .more-vendors {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--themed-very-fair);
    color: var(--themed-very-muted);
    border-radius: 25%;
    width: 2rem;
    height: 2rem;
    font-size: 0.938rem;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
  }
  .simplebar-wrapper {
    background: var(--themed-light);
    border-radius: 0.5rem;
  }
  .contract-background{
    background: var(--themed-light) !important;
  }
  .contacts-bar {
    min-width: 10rem;
  }
  .reset-btn {
    margin-top: 0.3rem;
    color: $danger;
    font-size: 1.1rem;
  }
  .clear-btn:hover {
    background: red;
  }
  
  .table th { background-color: var(--themed-lighter) !important; }
  .table td { font-size: inherit; }
  .month-box {
    border: 0.063rem solid var(--themed-light);
  }

  .custom-select {
    border: 0.063rem solid var(--themed-dark-drawer-bg);
    padding: 0.5rem 2.5rem 0.5rem 0.75rem;
    font-size: 0.055rem;
    width: 12rem !important;
    background-image: url('data:image/svg+xml;utf8,<svg fill="%23fff" height="16" viewBox="0 0 24 24" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/></svg>');
    background-size: 1rem;
    color: white;
  }
  .calendar-grid .month-box:nth-child(1){
    border-top-left-radius: 0.5rem;
  }
  .calendar-grid .month-box:nth-child(4){
    border-top-right-radius: 0.5rem;
  }
  .calendar-grid .month-box:nth-child(9){
    border-bottom-left-radius: 0.5rem;
  }
  .calendar-grid .month-box:nth-child(12){
    border-bottom-right-radius: 0.5rem;
  }
  .radio-btn {
    padding: 0.25rem 0.75rem;
    color: white;
  }
  .btn-outline-not-as-light{
    color: var(--themed-dark);
  }

  .sweet-modal .sweet-title {
    font-size: 1.25rem;
    font-weight: bold;
  }

  .sweet-modal-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.625rem;
  }

  .sweet-modal-table th,
  .sweet-modal-table td {
    padding: 0.625rem;
    text-align: left;
    border: 0.063rem solid #ddd;
  }

  .sweet-modal-table th {
    background-color: var(--themed-light);
    font-weight: bold;
  }

  .sweet-modal-table tbody tr {
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
  }

  .sweet-modal-table tbody tr:hover {
    background-color: var(--themed-light);
  }
  ::v-deep(.sweet-modal.theme-light) {
    max-height: 85vh !important;
  }
</style>
<style lang="scss">
  .scrollable-table-outer-wrap {
    background: var(--themed-box-bg);
  }
</style>
