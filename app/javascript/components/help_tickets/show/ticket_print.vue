<template>
  <div
    ref="ticketPrint"
    class="print-page text-color"
  >
    <div class="border p-2 custom-content">
      <h6 class="font-weight-bold">
        {{ ticketHeading }}
      </h6>
      <span class="small">
        {{ ticketTime }}
      </span>
    </div>

    <div class="border mt-3 pr-3 pl-3 custom-content">
      <div
        v-for="(field, index) in ticketFields"
        :key="index"
        class="row"
        :class="{ 'border-top': index > 0 }"
      >
        <span class="col-md-4 break-word">
          <p class="font-weight-bold p-1 mb-0"> {{ humanizeWord(field.label) }} </p>
        </span>
        <div class="col-md-8 border-left p-1">
          <div v-if="field.fieldAttributeType == 'rich_text'">
            <div
              ref="noteBox"
              class="note-box w-100"
              v-html="note(field.customFormValue.valueStr)"
            />
          </div>
          <p
            v-else
            class="mb-0"
          >
            {{ fieldValue(field, field.customFormValue) }}
          </p>
        </div>
      </div>
    </div>
    <div
      v-if="descriptionField && descriptionValue"
      class="custom-content"
    >
      <h6 class="p-2 mt-3 mb-0 section-background">
        {{ humanizeWord(descriptionField.name) }}
      </h6>
      <div
        ref="noteBox"
        class="note-box w-100 border p-2"
        v-html="note(descriptionValue)"
      />
    </div>
    <div v-if="isComments">
      <h6 class="p-2 mt-3 mb-0 section-background">
        Comments
      </h6>
      <div class="custom-content">
        <div
          v-for="(comment, index) in filteredComments"
          :key="index"
          class="border px-2 py-5 ticket-image-wrapper"
        >
          <div>
            <span class="font-weight-bold mr-2">
              {{ commentorName(comment) }}
            </span>
            <span class="small">
              {{ formatDateTime(comment.createdAt) }}
            </span>
            <div
              ref="noteBox"
              class="note-box w-100"
              v-html="note(comment.commentBody)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import printJS from "print-js";
  import string from "mixins/string";
  import linkIcons from 'mixins/link_icons';
  import { mapGetters } from "vuex";

  export default {
    mixins: [string, linkIcons],
    data() {
      return {
        smartListValues: null,
        list: [],
      };
    },
    computed: {
      ...mapGetters([
        'comments',
        'currentHelpTicket',
        'smartListFieldValues',
      ]),
      filteredComments() {
        return this.comments.filter(comment  => 
          comment.source
        );
      },
      ticketHeading() {
        if (this.currentHelpTicket) {
          return `#[${this.currentHelpTicket.ticketNumber}] ${this.subject}`;
        }
        return null;
      },
      ticketTime() {
        if (this.currentHelpTicket) {
          const created = this.formatDateTime(this.currentHelpTicket.createdAt);
          const updated = this.formatDateTime(this.currentHelpTicket.updatedAt);
          if (created === updated) {
            return `Created: ${created}`;
          }
          return `Created: ${created}, Updated: ${updated}`;
        }
        return null;
      },
      ticketFields() {
        const unWantedFields = ['description', 'subject'];
        return this.currentHelpTicket.customForm.formFields.filter(
          (field) => !unWantedFields.includes(field.name)
        );
      },
      descriptionField() {
        return this.currentHelpTicket.customForm.formFields.find(
          (field) => field.name === 'description'
        );
      },
      descriptionValue() {
        if (this.descriptionField) {
          return this.descriptionField.customFormValue.valueStr;
        }
        return null;
      },
      isComments() {
        return this.comments.length > 0;
      },
      subject() {
        return this.currentHelpTicket.customForm.formFields.find(
          (field) => field.name === 'subject'
        ).customFormValue.valueStr;
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.markLargeImageWrappers();
      });
    },
    methods: {
      markLargeImageWrappers() {
        this.$nextTick(() => {
          const wrappers = document.querySelectorAll('.ticket-image-wrapper');

          wrappers.forEach(wrapper => {
            const images = wrapper.querySelectorAll('img');
            let hasTallImage = false;

            images.forEach(img => {
              const attrHeight = parseInt(img.getAttribute('height'), 10);
              const actualHeight = img.clientHeight;
              if (attrHeight >= 250 || actualHeight >= 250) {
                hasTallImage = true;
              }
            });

            if (hasTallImage) {
              wrapper.classList.add('prevent-break');
            } else {
              wrapper.classList.remove('prevent-break');
            }
          });
        });
      },
      printHelpTicket() {
        this.markLargeImageWrappers();
        printJS({
          printable: this.$refs.ticketPrint,
          type: 'html',
          showModal: true,
          modalMessage: 'Printing Help Ticket...',
          maxWidth: '100%',
          style: `@page {
            size: A4;
            margin: 0.5in 0.5in 0.5in 0.4in;
          }`,
          documentTitle: this.subject,
          targetStyles: ['*'],
          font: 'Helvetica',
        });
      },
      formatDateTime(timestamp) {
        const date = new Date(timestamp);
        const convertedDate = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        });
        const convertedTime = date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
        });

        return `${convertedTime.toLowerCase()}, ${convertedDate.toUpperCase()}`;
      },
      fieldValue(field, value) {
        const notPresent = 'Not Specified';
        if (field.name === 'attachments') {
          return value.map((val) => val.attachment.attachmentFileName)?.join(', ') || notPresent;
        }

        if (Array.isArray(value)) {
          if (['tag', 'checkbox'].includes(field.fieldAttributeType)) {
            return value.map((val) => val.valueStr)?.join(', ') || notPresent;
          }
          return this.smartListFieldValues[field.name]?.join(', ') || notPresent;
        }

        if (value.valueStr) {
          if (field.fieldAttributeType === 'list') {
            return value.valueStr.length === 0 ? notPresent : value.valueStr.replace("\n", ', ');
          }
          return value.valueStr;
        } else if (value.valueInt) {
          return value.valueInt;
        }

        return notPresent;
      },
      note(value) {
        const html = value;
        let htmlObject = document.createElement('div');

        htmlObject.innerHTML = html;
        const links = htmlObject.getElementsByTagName('a');
        if (links.length > 0) {
          this.getIcon(links);
        }

        const images = htmlObject.getElementsByTagName('img');

        if (images) {
          images.forEach((img) => {
            if (!img.complete || img.naturalWidth === 0) {
              img.src = '';
              img.alt = 'Image not found.';
            } else {
              img.width = 200;
              img.style.height = '100%';
            }
          });
        }
        htmlObject = htmlObject.outerHTML;
        return htmlObject;
      },
      commentorName(comment) {
        return comment.contributor ? comment.contributor.name : this.checkComment(comment);
      },
      checkComment(comment) {
        return comment.source === "auto_generated" ? 'Auto Generated' : 'System';
      },
    },
  };
</script>

<style lang='scss' scoped>
.custom-content {
  width: 65rem;
}
.text-color {
  color: black;
}
.section-background {
  background-color: $gray-400;
  width: 5.5rem;
}
.prevent-break {
  break-inside: avoid;
  page-break-inside: avoid;
}
@media print {
  .print-page {
    page-break-after: always;
    white-space: nowrap;
  }
}
</style>
