<template>
  <div class="row">
    <div class="col-md-6 col-sm-12">
      <p class="font-weight-bold mb-1">
        <u>Basic Information</u>
      </p>

      <p
        class="font-weight-bold mb-0"
        style="word-wrap: break-word;"
      >
        IP Address
      </p>
      <p v-if="currentDiscoveredAsset.ipAddress">
        {{ currentDiscoveredAsset.ipAddress }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        MAC Addresses
      </p>
      <p>
        <span class="d-inline-block align-middle">
          <ul class="mb-0">
            <li
              v-for="macAddress in currentDiscoveredAsset.macAddresses"
              :key="macAddress"
            >
              {{ macAddress }}
            </li>
          </ul>
        </span>
      </p>

      <p class="font-weight-bold mb-0">
        Secondary MAC Addresses
      </p>
      <p>
        <span class="d-inline-block align-middle">
          <ul class="mb-0">
            <li
              v-for="macAddress in currentDiscoveredAsset.secondaryMacAddresses"
              :key="macAddress"
            >
              {{ macAddress }}
            </li>
          </ul>
        </span>
      </p>

      <p class="font-weight-bold mb-0">
        Manufacturer
      </p>
      <p v-if="currentDiscoveredAsset.manufacturer">
        {{ currentDiscoveredAsset.manufacturer }}
      </p>
      <p v-else>
        -
      </p>

      <p
        class="font-weight-bold mb-0"
        style="word-wrap: break-word;"
      >
        Asset Type
      </p>
      <p v-if="currentDiscoveredAsset.assetType">
        {{ currentDiscoveredAsset.assetType }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        Memory
      </p>
      <p v-if="currentDiscoveredAsset.hardwareDetail && currentDiscoveredAsset.hardwareDetail.memory">
        {{ currentDiscoveredAsset.hardwareDetail.memory }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">IMEI</p>
      <p>{{ imei }}</p>

      <p class="font-weight-bold mb-0">
        Hard Disk
      </p>
      <p v-if="currentDiscoveredAsset.hardwareDetail && currentDiscoveredAsset.hardwareDetail.hardDrive">
        {{ currentDiscoveredAsset.hardwareDetail.hardDrive }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        Machine Serial No.
      </p>
      <p v-if="currentDiscoveredAsset.machineSerialNo">
        {{ currentDiscoveredAsset.machineSerialNo }}
      </p>
      <p v-else>
        -
      </p>
    </div>

    <div class="col-md-6 col-sm-12">
      <p class="font-weight-bold mb-1">
        <u>Processor Information</u>
      </p>

      <p class="font-weight-bold mb-0">
        Name
      </p>
      <p v-if="currentDiscoveredAsset.hardwareDetail && currentDiscoveredAsset.hardwareDetail.processor">
        {{ currentDiscoveredAsset.hardwareDetail.processor }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        Cores
      </p>
      <p v-if="currentDiscoveredAsset.hardwareDetail && currentDiscoveredAsset.hardwareDetail.processorCores">
        {{ currentDiscoveredAsset.hardwareDetail.processorCores }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-1">
        <u>OS Information</u>
      </p>

      <p class="font-weight-bold mb-0">
        Operating System
      </p>
      <p v-if="currentDiscoveredAsset.os">
        {{ currentDiscoveredAsset.os }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        Name
      </p>
      <p v-if="currentDiscoveredAsset.osName">
        {{ currentDiscoveredAsset.osName }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        Version
      </p>
      <p v-if="currentDiscoveredAsset.osVersion">
        {{ currentDiscoveredAsset.osVersion }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        OS Serial No.
      </p>
      <p v-if="currentDiscoveredAsset.osSerialNo">
        {{ currentDiscoveredAsset.osSerialNo }}
      </p>
      <p v-else>
        -
      </p>

      <p class="font-weight-bold mb-0">
        Firmware
      </p>
      <p v-if="currentDiscoveredAsset.firmware">
        {{ currentDiscoveredAsset.firmware }}
      </p>
      <p v-else>
        -
      </p>
    </div>
  </div>
</template>

<script>

import { mapGetters } from 'vuex';

export default {
  computed: {
    ...mapGetters(['currentDiscoveredAsset']),
    imei() {
      return this.currentDiscoveredAsset?.hardwareDetail?.imei || '-';
    },
  },
};
</script>
