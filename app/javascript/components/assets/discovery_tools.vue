<template>
  <div class="row px-5 mx-5 mt-n3">
    <div
      v-if="shouldHighlightAgent"
      class="col-12"
    >
      <h4 class="mt-3">
        Discovery Agent
        <span class="not-as-small text-muted mt-1 d-inline-flex highlight-alert py-1 px-2 mb-1 ml-2 d-flex align-center">
          <span class="icon genuicon-device-health mr-1 ml-0 not-as-small h6 mb-0 mt-0.5 position-relative" />
          Risk Center requires discovery agent
        </span>
        <div class="not-as-small text-muted mt-2">
          Keep your device info up-to-date and hassle-free with this read-only password-protected application.
        </div>
      </h4>
      <div class="row">
        <add-onboarding-item
          v-for="(option, index) in filteredOnboardingOptions.slice(0, 2)"
          :key="`${option.name}-${index}`"
          :add-item-option="option"
          :intg-detail="findCompanyIntegration(option.searchableName)"
          :loading-value="loadingValue[option.searchableName]"
          :is-customizable="isCustomizable(option.searchableName)"
          class="col-md-4 mt-3"
          :class="{'highlight-card': option.name === 'Discovery Agent' && shouldHighlightAgent}"
          @open-sync-modal="openSyncModal"
          @open-delete-modal="openDeleteModal"
          @customize-integration="openSyncCustomize"
        />
      </div>

      <h4 class="mt-6 mb-n2">More Connectors</h4>
      <div class="row">
        <add-onboarding-item
          v-for="(option, index) in filteredOnboardingOptions.slice(2)"
          :key="index"
          :add-item-option="option"
          :intg-detail="findCompanyIntegration(option.searchableName)"
          :loading-value="loadingValue[option.searchableName]"
          :is-customizable="isCustomizable(option.searchableName)"
          class="col-md-4 mt-5"
          :class="{'highlight': option.name === 'Discovery Agent' && shouldHighlightAgent}"
          @open-sync-modal="openSyncModal"
          @open-delete-modal="openDeleteModal"
          @customize-integration="openSyncCustomize"
        />
      </div>
    </div>

    <div
      v-else
      class="row"
    >
      <add-onboarding-item
        v-for="(option, index) in filteredOnboardingOptions"
        :key="index"
        :add-item-option="option"
        :intg-detail="findCompanyIntegration(option.searchableName)"
        :loading-value="loadingValue[option.searchableName]"
        :is-customizable="isCustomizable(option.searchableName)"
        class="col-md-3 mt-5"
        :class="{'highlight': option.name === 'Discovery Agent' && shouldHighlightAgent}"
        @open-sync-modal="openSyncModal"
        @open-delete-modal="openDeleteModal"
        @customize-integration="openSyncCustomize"
        @show-integration-details="showIntegrationDetails"
      />
    </div>

    <add-item-modal
      ref="addItemModal"
      @close="resetActiveAddMethod"
    >
      <component
        :is="activeAddMethod"
        :customizing.sync="customizing"
        @close="closeSyncModal"
      />
    </add-item-modal>

    <delete-resync-item-modal
      ref="deleteItemModal"
      :integration="activeDeleteIntg"
      :integration-type="'assets'"
      @set-resync-pusher="setResyncPusher"
    />
    <sweet-modal
      ref="detailsModal"
      v-sweet-esc
      :title="'Sync Account'"
      class="details-modal"
      width="75%"
      blocking
    >
      <details-modal
        v-if="showDetailsModal"
        ref="detailModalComponent"
        :header="header"
        :sub-header="subHeader"
        :image-src="imageSrc"
        :intg-details="intgDetails"
      />
    </sweet-modal>
  </div>
</template>

<script>
  import { mapGetters, mapMutations } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import http from 'common/http';
  import onboardingOptions from './onboarding_options';
  import AddItemModal from '../shared/module_onboarding/add_item_modal.vue';
  import DeleteResyncItemModal from '../shared/module_onboarding/delete_resync_item_modal.vue';
  import AddOnboardingItem from '../shared/module_onboarding/add_onboarding_item.vue';
  import * as addAssetModalPages from './add_asset_modal_pages/index';
  import common from '../shared/module_onboarding/common';
  import DetailsModal from './add_asset_modal_pages/connectors/details/details_modal.vue';

  const CUSTOMIZABLE_INTEGRATIONS = ['meraki', 'ubiquiti', 'aws_assets', 'google_assets'];
  const PUSHER_SETUP_INTEGRATIONS = [...CUSTOMIZABLE_INTEGRATIONS, 'azure_assets', 'ms_intune_assets', 'azure_ad_assets', 'kaseya', 'kandji', 'jamf_pro', 'mosyle', 'google_workspace'];

  export default {
    components: {
      AddItemModal,
      DeleteResyncItemModal,
      AddOnboardingItem,
      DetailsModal,
      SweetModal,
      ...addAssetModalPages,
    },
    mixins: [onboardingOptions, common, permissionsHelper],
    data() {
      return {
        pusherIntegrations: PUSHER_SETUP_INTEGRATIONS,
        activeAddMethod: null,
        activeDeleteIntg: null,
        fetchedIntegrations: false,
        customizing: false,
        loadingValue: PUSHER_SETUP_INTEGRATIONS.reduce((acc, int) => ({ ...acc, [int]: 0.1 }), {}),
        shouldHighlightAgent: this.isHighlightAgentURL(),
        header:"",
        subHeader:"",
        imageSrc:"",
        intgDetails: {},
        showDetailsModal: false,
        integrationMeta: {
          meraki: {
            header: "Integrate Cisco Meraki",
            subHeader: "Sync your existing asset services with Cisco Meraki",
            imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/meraki.png",
          },
          kaseya: {
            header: "Integrate Kaseya",
            subHeader: "Sync your existing asset services with Kaseya",
            imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/kaseya.png",
          },
          ubiquiti: {
            header: "Integrate Ubiquiti",
            subHeader: "Sync your existing asset services with Ubiquiti",
            imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/Ubiquiti_Networks_logo.png",
          },
          mosyle: {
            header: "Integrate Mosyle",
            subHeader: "Sync your existing asset services with Mosyle",
            imageSrc: "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/logos/integrations-logos/mosyle.png",
          },
          kandji: {
            header: "Integrate Kandji",
            subHeader: "Sync your existing asset services with Kandji",
            imageSrc: "https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/kandji.png",
          },
        },
        showGoogleWorkspaceIntegration: false,
      };
    },
    computed: {
      ...mapGetters([
        'companyIntegrations',
        'totalUnfilteredRecords',
        'merakiInfo',
      ]),
      filteredOnboardingOptions() {
        let filteredOptions = this.onboardingOptions;

        if (this.$route.path === '/discovery_tools/connectors') {
          filteredOptions = filteredOptions.filter(option => !['Manual Entry', 'Asset Import'].includes(option.name));
        }
        if (this.showGoogleWorkspaceIntegration) {
          filteredOptions = filteredOptions.filter(option => option.name !== 'Google Workspace');
        }

        return filteredOptions;
      },
      noAssetsTotal() {
        return this.totalUnfilteredRecords != null && this.totalUnfilteredRecords === 0;
      },
    },
    watch: {
      totalRecord() {
        this.fetchIntegrations();
      },
    },
    beforeDestroy() {
      this.unsubscribePusher();
    },
    methods: {
      ...mapMutations([
        'setMerakiInfo',
      ]),
      onWorkspaceChange() {
        this.displayGoogleWorkspaceIntegration();
        this.fetchIntegrations();
        this.setupPusherListeners();
      },
      openSyncModal(method) {
        this.activeAddMethod = method;
        this.$refs.addItemModal.open();
      },
      setResyncPusher(intgName) {
        this.loadingValue[intgName] = 0.1;
      },
      closeSyncModal() {
        this.$refs.addItemModal.close();
      },
      openDeleteModal(intg) {
        this.activeDeleteIntg = intg;
        this.$refs.deleteItemModal.open();
      },
      openSyncCustomize(intg) {
        this.customizing = true;
        this.openSyncModal(intg);
      },
      showIntegrationDetails(intg) {
        const meta = this.integrationMeta[intg.name];
        if (meta) {
          this.header = meta.header;
          this.subHeader = meta.subHeader;
          this.imageSrc = meta.imageSrc;
        } else {
          this.header = `Integration`;
          this.subHeader = "Details for this integration";
          this.imageSrc = "https://example.com/default.png";
        }
        this.showDetailsModal = true;
        this.intgDetails = intg;
        this.$nextTick(() => {
          this.$refs.detailModalComponent.getAssetCount();
          this.$refs.detailsModal.open();
        });
      },
      resetActiveAddMethod() {
        this.activeAddMethod = null;
        this.customizing = false;
      },
      findCompanyIntegration(integrationName) {
        if (this.companyIntegrations) {
          return this.companyIntegrations.find(({ name }) => name === integrationName);
        }
        return null;
      },
      fetchIntegrations() {
        if ((this.$route.path === "/assets" && this.noAssetsTotal) || this.$route.path === "/discovery_tools/connectors" && !this.fetchedIntegrations) {
          this.fetchedIntegrations = true;
          this.$store.dispatch('fetchCompanyIntegrations');
        }
      },
      isCustomizable(integration) {
        return CUSTOMIZABLE_INTEGRATIONS.includes(integration);
      },
      isHighlightAgentURL() {
        const queryString =   this.$route.fullPath.split('?')[1];
        
        if (queryString) {
          const params = new URLSearchParams(queryString);
          return params.get('setup') === 'risk_center';
        }
        return false;
      },
      displayGoogleWorkspaceIntegration() {
        http
          .get('/company_integrations/display_google_workspace_integration.json')
          .then((res) => {
            this.showGoogleWorkspaceIntegration = res.data.showGoogleWorkspaceIntegration;
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
.highlight-card {
  :deep(.flex-column) {
    background: tint-color($blue, 95%);
    border: 2px solid $themed-link;
  }
}

.highlight-alert {
  background: tint-color($blue, 95%);
  /* background: var(--themed-light); */
  border-left: 4px solid tint-color($blue, 85%);
  border-left: 4px solid $themed-link;
  border-radius: $border-radius-sm;
  display: block;
  position: absolute;
}

.details-modal {
  :deep {
    .sweet-title {
      background-color: $themed-dark-drawer-bg;
      color: white;
      font-size: 1rem;
      height: 3.5rem;

    }

    .sweet-modal {
      margin-top: 0.3rem;
    }

    .sweet-action-close {
      color: white !important;
    }

    .sweet-content-content {
      width: 100%;
    }
    .sweet-content {
      padding-top: 0.8rem;
      padding-bottom: 0rem;
    }
  }
}
</style>
