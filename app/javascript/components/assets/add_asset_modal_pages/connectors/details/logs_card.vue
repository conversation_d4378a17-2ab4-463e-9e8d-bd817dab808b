<template>
  <div>
    <div class="box box-size p-2 align-items-start">
      <div class="d-flex w-100 mt-1 justify-content-center">
        <div>
          <h6 class="font-weight-semi-bold my-1 d-flex">
            Synchronization Logs
          </h6>
        </div>
      </div>
      <div class="w-100 ml-2 mt-1">
        <div class="d-flex w-100 justify-content-between">
          <h6 class="font-weight-bold-small not-as-small ml-1 my-1 d-flex">
            Current Status
          </h6>
          <div class="d-flex justify-content-end">
            <h6 class="font-weight-bold-small not-as-small my-1 mr-1 d-flex">
              {{ details.active  ? 'Active' : 'Inactive' }}
            </h6>
            <i
              class="connetor-pill mr-1"
              :style="{background: getLogscolor()}"
            />
          </div>
        </div>
        <div class="d-flex w-100 justify-content-between">
          <h6 class="font-weight-bold-small not-as-small ml-1 my-1 d-flex">
            Last Sync Time
          </h6>
          <h6 class="font-weight-bold-small not-as-small my-1 mr-1 d-flex">
            {{ getSyncDate(details.lastSyncedAt) }}
          </h6>
        </div>
      </div>
      <div class="w-100 ml-2 mt-1">
        <div>
          <h6 class="small text-grey my-1 ml-1 d-flex">
            LOGS:
          </h6>
        </div>
      </div>
      <div
        v-if="isLoading"
        class="box__inner box-height mt-3"
      >
        <h5 class="text-secondary font-weight-normal">
          Loading
          <span class="ml-3 d-inline-block">
            <sync-loader
              :loading="true"
              class="ml-3 mt-1"
              color="#0d6efd"
              size="0.5rem"
            />
          </span>
        </h5>
      </div>

      <div
        v-else-if="connectorLogs && connectorLogs.length === 0"
        class="box__inner box-height mt-3"
      >
        <h4>There are no logs present.</h4>
      </div>

      <div
        v-else-if="connectorLogs && connectorLogs.length > 0"
        class="box__inner scrollable-table-wrapper insight-scroll"
        @scroll="handleScroll"
      >
        <div>
          <table class="table col-12 log-table">
            <tbody>
              <tr
                v-for="log in connectorLogs"
                :key="log.id"
                class="text-secondary"
              >
                <td class="py-2">
                  <div class="d-flex">
                    <span class="small date-box">{{ formatDate(log.createdAt) }}</span>
                  </div>
                </td>
                <td class="justify-log text-capitalize py-2">
                  <span class="small">
                    {{ getStatusMessage(log) }}
                  </span>
                  <i
                    class="description-pill mr-1 ml-1"
                    :style="{background: getLogscolor(log)}"
                  />
                </td>
              </tr>
              <tr
                v-if="loadingMore"
                class="text-center"
              >
                <td
                  colspan="2"
                  class="py-3"
                >
                  <sync-loader
                    :loading="true"
                    color="#0d6efd"
                    size="0.5rem"
                  />
                  <span class="ml-2 text-secondary">Loading more logs...</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import MomentTimezone from 'mixins/moment-timezone';
  import strings from 'mixins/string';
  import http from 'common/http';
  import permissionsHelper from 'mixins/permissions_helper';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';

  export default {
    components: {
      SyncLoader,
    },
    mixins: [permissionsHelper, MomentTimezone, strings],
    props: {
      connector: {
        type: String,
        default: '',
      },
      details: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        connectorLogs: [],
        perPage: 25,
        pageCount: 1,
        indexPage: 0,
        isLoading: true,
        loadingMore: false,
        hasMoreData: true,
        title: null,
        connectorName: 'all',
        connectorNames: [
          { name: 'All', value: 'all' },
          { name: 'Cisco Meraki', value: 'meraki' },
          { name: 'Ubiquiti', value: 'ubiquiti' },
          { name: 'Microsoft Intune', value: 'ms_intune' },
          { name: 'Azure AD Devices', value: 'azure_ad_devices' },
          { name: 'Azure', value: 'azure' },
          { name: 'GCP', value: 'google' },
          { name: 'AWS', value: 'aws' },
          { name: 'Kaseya', value: 'kaseya' },
          { name: 'Kandji', value: 'kandji' },
          { name: 'Jamf Pro', value: 'jamf_pro' },
          { name: 'Probe', value: 'probe' },
          { name: "Mosyle", value: "mosyle" },
        ],
        colors: {
          successful: '#5AB379',
          failed: '#E15241',
          pending: '#EDB400',
        },
      };
    },
    watch: {
      connector: {
        handler() {
          this.getConnectorsLogs();
        },
        immediate: true,
      },
    },
    methods: {
      getLogscolor(log) {
        if (log) {
          if (['credentials_updated', 'credentials_added'].includes(log.action) && log.status === 'successful') {
            return this.colors.pending;
          }
          return this.colors[log.status] || this.colors.successful;
        }
        const {syncStatus} = this.details;
        if (syncStatus && this.colors[syncStatus]) {
          return this.colors[syncStatus];
        }
        return this.colors.pending;
      },
      getSyncDate(date) {
        return this.timezoneMoment(date, Vue.prototype.$timezone);
      },
      formatDate(dateString) {
        const date = new Date(dateString);
        return `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
      },
      getConnectorsLogs() {
        const params = {
          per_page: this.perPage,
          page: this.indexPage + 1,
          connector_name: this.connector,
        };
        http
          .get('/asset_connector_logs.json', { params })
          .then((res) => {
            if (this.indexPage === 0) {
              this.connectorLogs = res.data.assetConnectorLogs;
            } else {
              this.connectorLogs = [...this.connectorLogs, ...res.data.assetConnectorLogs];
            }
            this.pageCount = res.data.pageCount;
            this.hasMoreData = res.data.assetConnectorLogs.length === this.perPage && this.indexPage + 1 < this.pageCount;
            this.isLoading = false;
            this.loadingMore = false;
          })
          .catch((e) => {
            this.emitError(`Sorry, there was an error fetching logs. ${e.response.data.message}`);
            this.isLoading = false;
            this.loadingMore = false;
          });
      },
      getStatusMessage(log) {
        if (['sync', 'resync', 'activated'].includes(log.action)) {
          if (log.status === 'successful') {
            return `${log.action}ed successfully`;
          }
          return `${log.action} failed`;
        }

        if (['credentials_added', 'credentials_updated'].includes(log.action)) {
          if (log.action === 'credentials_added') {
            return `Credentials were added by ${log.ownerName ? log.ownerName : log.userName}`;
          }
          return `Credentials were updated by ${log.ownerName ? log.ownerName : log.userName}`;
        }

        if (['deleted', 'deactivated'].includes(log.action)) {
          if (log.status === 'successful') {
            return `Integration ${log.action} successfully`;
          }
          return `Integration ${log.action} failed`;
        }

        return `${log.action} ${log.status}`;
      },
      handleScroll(event) {
        const { scrollTop, scrollHeight, clientHeight } = event.target;
        const threshold = 10;
        
        if (scrollTop + clientHeight >= scrollHeight - threshold && 
            this.hasMoreData && 
            !this.loadingMore && 
            !this.isLoading) {
          this.loadMore();
        }
      },
      loadMore() {
        if (!this.hasMoreData || this.loadingMore) return;
        
        this.loadingMore = true;
        this.indexPage += 1;
        this.getConnectorsLogs();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon-genuicon-refresh {
    background-color: $color-home;
  }

  .action-icon-nulodgicon-edit {
    background-color: $yellow;
  }

  .action-icon-nulodgicon-trash-b {
    background-color: $red;
  }

  .source-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .text-align-start {
    text-align: start;
  }

  .box-height {
    max-height: 14.438rem;
  }

  .scrollable-table-wrapper {
    max-height: 14.438rem;
    overflow-y: auto;
  }

  .log-table thead th {
    position: sticky;
    top: 0;
    z-index: 2;
  }

  .log-table {
    border: none;
  }

  .log-table td {
    border: none;
  }

  .log-table tr {
    border: none;
  }

  .log-table tbody tr {
    border: none;
  }
  .date-box {
    flex: none;
  }
  .description-pill {
    display: inline-block;
    border-radius: 50%;
    height: 0.4rem;
    width: 0.4rem;
  }
  .connetor-pill {
    display: inline-block;
    border-radius: 50%;
    height: 0.5rem;
    width: 0.5rem;
    margin-top: 0.5rem;
  }
  .justify-log {
    display: flex;
    text-align: right;
    justify-content: flex-end;
    align-items: center;
  }
</style>
