<template>
  <div>
    <modal-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <div class="mt-2"/>
    <div
      v-if="isLoading"
      class="text-center"
    >
      Please wait while we fetch your {{intgDetails.name}} account locations and networks, don't close this window.
      <sync-loader
        class="mt-3"
        color="#0d6efd"
        size="0.5rem"
      />
    </div>
    <template>
      <div class="subpage-menu py-1/2">
        <div
          v-for="page in pages"
          :key="page.name"
          :class="['clickable subpage-menu__item ', isActiveClass(page.name)]"
          @click="setViewPage(page)"
        >
          {{ page.label }}
        </div>
      </div>
    </template>
    <div class="mt-2 mb-0">
      <integration-creds
        :value="intgDetails"
      />
      <hr 
        v-if="activeTab === 'Config'"
        class="my-0"
      >
      <div v-if="activeTab === 'Config'">
        <div class="d-flex justify-content-end">
          <connector-stats
            v-if="widgetsInsightsData('device').data.length && !hideClientStats"
            class="col-4 my-3"
            :data="widgetsInsightsData('device')"
            :connector-name="intgDetails.name"
            :is-modal-list="true"
          />
          <chart-data-card
            :is="'chart-data-card'"
            v-if="widgetsInsightsData('client').data.length"
            module-name="assetConnector"
            class="my-3"
            :class="[hideClientStats ? 'col-8' : 'col-4']" 
            :card-title="'Device Sync Statistics'"
            :data="widgetsInsightsData('client')"
            :default-chart-type="'donut'"
            :intg-details="intgDetails"
            is-asset-connector
          />
          <logs-card
            class="col-4 my-3"
            :connector="intgDetails.name"
            :details="intgDetails"
          />
        </div>
      </div>
      <div v-else>
        <connector-logs :connector="intgDetails.name"/>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import common from 'components/shared/module_onboarding/common';
  import ChartDataCard from 'components/shared/chart_data_card.vue';
  import assetHelper from 'mixins/assets/asset_helper';
  import permissionsHelper from 'mixins/permissions_helper';
  import ConnectorStats from './connector_stats.vue';
  import ModalTitle from  './modal_title.vue';
  import IntegrationCreds from './integration_creds.vue';
  import ConnectorLogs from "./connector_logs.vue";
  import LogsCard from './logs_card.vue';

  export default {
    components: {
      ModalTitle,
      SyncLoader,
      IntegrationCreds,
      ChartDataCard,
      ConnectorStats,
      ConnectorLogs,
      LogsCard,
    },
    mixins: [common, permissionsHelper, assetHelper],
    props: ['header', 'subHeader', 'imageSrc', 'intgDetails'],
    data() {
      return {
        activeTab: 'Config',
        pages: [
          { name: 'Config', label: 'Config' },
          { name: 'Logs', label: 'Logs' },
        ],
        assetCounts: {},
      };
    },
    methods: {
      isActiveClass(page) {
        return { 'active-tab': this.activeTab === page };
      },
      setViewPage(page) {
        this.activeTab = page.name;
      },
      getAssetCount() {
        const params = { source: this.intgDetails.name };
        http
          .get("/discovered_assets/asset_count_by_type.json", { params })
          .then((res) => {
            if (res.data.assetCounts) {
              this.assetCounts = res.data.assetCounts;
            }
          }).catch(() => {
            this.emitError(`Sorry, there was an error fetching the assets count.`);
          });
        },
        widgetsInsightsData(type) {
          const counts = this.assetCounts || {};
          const dataSource = this.hideClientStats ? counts : counts[type];

          return dataSource ? { labels: Object.keys(dataSource), data: Object.values(dataSource) } : { labels: [], data: [] };
        },
    },
  };
</script>
<style scoped>
  .active-tab {
    background-color: var(--themed-assets-light);
    color: var(--themed-assets-dark);
  }

  .widget-card:hover {
    .drag-overlay {
      opacity: 1;
      visibility: visible;
    }
  }
  .box-widget {
    min-height: 9.375rem;
    max-height: 15.625rem;

    .box-size {
      background-color: transparent;
      box-shadow: none;
      padding: 0;
    }
  }

  .box-height {
    min-height: 22.75rem;
  }

  .height-auto {
    height: auto;
  }
</style>
