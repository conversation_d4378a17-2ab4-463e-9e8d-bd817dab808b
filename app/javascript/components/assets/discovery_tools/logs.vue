<template>
  <div
    class="mt-3 pb-5"
  >
    <div class="w-100 d-flex float-right justify-content-end mt-1 mb-1">
      <dropdown-filter
        id-label="value"
        label="Connectors"
        class="mb-sm-2 mb-md-0 mr-2"
        :include-all="false"
        :options="connectorNames"
        :value="connectorName"
        @selected="filterByConnectorName"
      />
      <span class="small">
        <span class="text-muted mr-1">
          <span>Results per page</span>
        </span>
        <select
          id="filtersPerPage"
          class="form-control form-control-sm d-inline-block select-per-page-filter"
          :input="perPage"
          :value="perPage"
          data-tc-filters-per-page
          @input="changePageSize"
        >
          <option>25</option>
          <option>50</option>
          <option>100</option>
        </select>
      </span>
      <div
        v-if="pageCount > 1"
        class="ml-3"
      >
        <paginate
          ref="paginate"
          :click-handler="pageSelected"
          :container-class="'pagination pagination-sm'"
          :next-class="'next-item'"
          :next-link-class="'page-link'"
          :next-text="'Next'"
          :page-class="'page-item'"
          :page-count="pageCount"
          :page-link-class="'page-link'"
          :prev-class="'prev-item'"
          :prev-link-class="'page-link'"
          :prev-text="'Prev'"
          :selected="indexPage"
        />
      </div>
    </div>
    <div
      v-if="isLoading"
      class="text-center mt-5 clear-both"
    >
      <h5 class="text-secondary font-weight-normal">
        Loading
        <span class="ml-3 d-inline-block">
          <sync-loader
            :loading="true"
            class="ml-3 mt-1"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </h5>
    </div>
    <div
      v-else-if="connectorLogs && connectorLogs.length === 0"
      class="text-center mt-5 clear-both"
    >
      <h4>There are no logs present.</h4>
    </div>
    <div v-else-if="connectorLogs && connectorLogs.length > 0">
      <table class="table col-12">
        <thead>
          <tr>
            <th class="col-2">Connectors</th>
            <th class="col-3">Actions</th>
            <th class="col-4">Details</th>
            <th class="col-1">Changes</th>
            <th class="col-2 text-right">Performed By</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="log in connectorLogs"
            :key="log.id"
            class="text-secondary"
          >
            <td>
              <div class="pr-sm-2 pr-md-3 d-flex align-items-center">
                <span
                  v-tooltip="toTitle(log.connectorName)"
                  class="position-relative"
                >
                  <img
                    class="source-icon mr-1 rounded-circle"
                    :src="getSourceIcon(log.connectorName)"
                  >
                </span>
                <span class="font-weight-bold text-capitalize ml-2"> {{ toTitle(log.connectorName) }} </span>
              </div>
            </td>
            <td>
              <div class="pr-sm-2 pr-md-3 d-flex align-items-center">
                <span
                  class="action-icon position-relative"
                  :class="`action-icon-${smartList(log.action)}`"
                >
                  <i :class="smartList(log.action)"/>
                </span>
                <span class="font-weight-bold text-capitalize ml-2">  {{ toTitle(log.action) }} </span>
              </div>
            </td>
            <td>
              <span class="font-weight-bold">
                {{ getStatusMessage(log) }}
              </span>
            </td>
            <td>
              <div class="pr-sm-2 pr-md-3 d-flex align-items-center">
                <span
                  v-if="shouldShowLogDetails(log)"
                  class="btn btn-link btn-sm clickable mr-2"
                  @click.prevent.stop="showChanges(log)"
                >
                  View Changes
                </span>
              </div>
            </td>
            <td>
              <div class="mt-sm-1 mt-md-0 text-right small text-muted p--responsive">
                <span>{{ log.ownerName ? log.ownerName : log.userName }}</span>
                <br>
                <span class="small">{{ createdAt(log.createdAt) }}</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <sweet-modal
      ref="moduleCredentialsUpdatedModal"
      :title="title"
    >
      <template>
        <module-logs-changes
          v-if="moduleCredentialsUpdated"
          :activity="moduleCredentialsUpdated.data"
          :is-asset-connector-logs="true"
          @close="closeEditModal"
        />
      </template>
    </sweet-modal>
  </div>
</template>
<script>
  import MomentTimezone from 'mixins/moment-timezone';
  import strings from 'mixins/string';
  import http from 'common/http';
  import Paginate from 'vuejs-paginate';
  import permissionsHelper from 'mixins/permissions_helper';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import { SweetModal } from 'sweet-modal-vue';
  import moduleLogsChanges from 'components/shared/module_logs_changes.vue';
  import DropdownFilter from '../../shared/dropdown_filter.vue';
  import assetImages from '../../../mixins/asset_images';

  export default {
    components:{
      SyncLoader,
      Paginate,
      SweetModal,
      moduleLogsChanges,
      DropdownFilter,
    },
    mixins: [permissionsHelper, MomentTimezone, strings, assetImages],
    data() {
      return {
        connectorLogs: [],
        perPage: 25,
        pageCount: 1,
        indexPage: 0,
        isLoading: true,
        moduleCredentialsUpdated: null,
        title: null,
        connectorName: "all",
        connectorNames: [
          { name: "All", value: "all" },
          { name: "Cisco Meraki", value: "meraki" },
          { name: "Ubiquiti", value: "ubiquiti" },
          { name: "Microsoft Intune", value: "ms_intune" },
          { name: "Azure AD Devices", value: "azure_ad_devices"} ,
          { name: "Azure", value: "azure" },
          { name: "GCP", value: "google" },
          { name: "AWS", value: "aws" },
          { name: "Kaseya", value: "kaseya" },
          { name: "Kandji", value: "kandji" },
          { name: "Jamf Pro", value: "jamf_pro" },
          { name: "Probe", value: "probe" },
          { name: "Agent", value: "agent" },
          { name: "Mosyle", value: "mosyle" },
        ],
      };
    },
    mounted() {
      this.getConnectorsLogs();
    },
    methods: {
      changePageSize(e) {
        this.perPage = e.currentTarget.value;
        this.indexPage = 0;
        this.getConnectorsLogs();
      },
      getConnectorsLogs() {
        const params = {
          per_page: this.perPage,
          page: this.indexPage + 1,
          connector_name: this.connectorName,
        };
        http
          .get('/asset_connector_logs.json',{params})
          .then(res => {
            this.connectorLogs = res.data.assetConnectorLogs;
            this.pageCount = res.data.pageCount;
            this.isLoading = false;
          })
          .catch(e => {
            this.emitError(`Sorry, there was an error fetching logs. ${e.response.data.message}`);
            this.isLoading = false;
          });
      },
      pageSelected(p) {
        this.indexPage = p - 1;
        this.isLoading = true;
        this.connectorLogs = [];
        this.getConnectorsLogs();
      },
      createdAt(createdAt) {
        return this.timezoneMoment(createdAt, Vue.prototype.$timezone);
      },
      smartList(action) {
        const iconMap = {
          sync: "genuicon-refresh",
          resync: "genuicon-refresh",
          credentials_added: "nulodgicon-edit",
          credentials_updated: "nulodgicon-edit",
          deactivated: "nulodgicon-edit",
          activated: "nulodgicon-edit",
          deleted: "nulodgicon-trash-b",
        };
        return iconMap[action] || "";
      },
      getStatusMessage(log) {
        const connector = this.toTitle(log.connectorName);
        const { action, status, connectorName } = log;

        const isSuccess = status === 'successful';

        if (['sync', 'resync', 'activated'].includes(action)) {
          const actionText = action === 'activated' ? action : `${action}ed`;
          return `${connector} ${actionText} ${isSuccess ? 'successfully' : 'failed'}`;
        }

        if (['credentials_added', 'credentials_updated'].includes(action)) {
          const verb = action === 'credentials_added' ? 'added' : 'updated';
          return `${connector} credentials were ${verb}`;
        }

        if (['deleted', 'deactivated'].includes(action)) {
          const needsIntegrationSuffix = !(connectorName === 'probe' || connectorName === 'agent');
          const target = needsIntegrationSuffix ? `${connector}integration` : connector;
          return `${target} ${action} ${isSuccess ? 'successfully' : 'failed'}`;
        }

        return `${action} ${status}`;
      },

      showChanges(log) {
        this.moduleCredentialsUpdated = log;
        this.setTitle(log.connectorName);
        this.$refs.moduleCredentialsUpdatedModal.open();
      },
      closeEditModal() {
        this.$refs.moduleSettingChangesModal.close();
      },
      setTitle(connectorName) {
        this.title = this.toTitle(`${connectorName} updated changes`);
      },
      filterByConnectorName(filter) {
        this.connectorName = filter;
        this.fetchLogsAndSetPage();
      },
      fetchLogsAndSetPage() {
        this.indexPage = 0;
        this.getConnectorsLogs();
      },
      shouldShowLogDetails(log) {
        return (
          log.action === 'credentials_updated' ||
          ['probe', 'agent'].includes(log.connectorName)
        );
      },
    },
  };
</script>

<style lang="scss" scoped>
  .action-icon-genuicon-refresh { background-color: $color-home; } //— sync + resync
  .action-icon-nulodgicon-edit { background-color: $yellow; } //— config updates
  .action-icon-nulodgicon-trash-b { background-color: $red; } //— deletion

  .source-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
</style>
