require 'rails_helper'

RSpec.describe Integrations::Mosyle::Config, type: :model do
  let(:company) { FactoryBot.create(:company) }
  let(:company_user) { FactoryBot.create(:user) }

  describe 'validations' do
    it { should validate_presence_of(:access_token) }
    it { should validate_presence_of(:company_id) }
  end

  let(:valid_config_attributes) do
    {
      username: 'test_user',
      password: 'test_password',
      access_token: 'valid_token',
      company_id: company.id,
      company_user_id: company_user.id
    }
  end

  context 'Association' do
    it "should have belongs_to relationship with company" do
      assoc = described_class.reflect_on_association(:company)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have belongs_to relationship with company_user" do
      assoc = described_class.reflect_on_association(:company_user)
      expect(assoc.macro).to eq :belongs_to
    end

    it "should have one relationship with company_integration" do
      assoc = described_class.reflect_on_association(:company_integration)
      expect(assoc.macro).to eq :has_one
    end
  end

  context "Callbacks Existence" do
    it { should callback(:create_comp_intg_and_execute_job).after(:commit) }
    it { should callback(:create_credentials_update_log).after(:update) }
    it { should callback(:destroy_integration_data).before(:destroy) }
  end
end
