require 'rails_helper'
require 'httparty'
include CompanyUserHelper

RSpec.describe Integrations::Mosyle::FetchData, type: :service do
  before(:all) do
    @company = FactoryBot.create(:company)
    @config = FactoryBot.create(:mosyle_config, company: @company)
    @service = described_class.new(@company.id, @config)
  end

  let(:mosyle_data) do
    {
      'username'     => '<EMAIL>',
      'password'     => 'supersecret',
      'access_token' => 'initial_token'
    }
  end

  describe '#token' do
    it 'fetches and returns the refresh_token and expires_in, and logs success' do
      fake_response = OpenStruct.new(
        parsed_response: {
          'token' => 'new_refresh_token',
          'expires'    => 7200
        },
        code: 200,
        success?: true,
        message: 'OK',
        body: '{"token":"new_refresh_token"}',
        headers: { 'authorization' => 'new_authorization_token', 'expires' => 7200 }
      )
      allow(HTTParty).to receive(:post).and_return(fake_response)

      result = @service.token(mosyle_data)

      expect(result['token']).to eq('new_authorization_token')
      expect(result['expires']).to eq(7200)
      expect(Logs::ApiEvent.where(
        company_id: @company.id,
        api_type: 'token',
        status: :success,
        class_name: described_class.name
      )).to exist
    end

    it 'returns error when credentials are invalid and logs error' do
      fake_response = OpenStruct.new(
        parsed_response: { 'error' => 'Invalid accessToken' },
        code: 401,
        success?: false,
        message: 'Unauthorized',
        body: '{"error":"Invalid accessToken"}',
        headers: { 'authorization' => nil, 'expires' => nil }
      )
      allow(HTTParty).to receive(:post).and_return(fake_response)

      result = @service.token(mosyle_data)

      expect(result['error']).to eq('Invalid accessToken')
      expect(Logs::ApiEvent.where(
        company_id: @company.id,
        api_type: 'token',
        status: :error,
        class_name: described_class.name
      )).to exist
    end
  end

  describe '#get_devices' do
    it 'fetches devices inventory successfully and logs a success event' do
      fake_response = OpenStruct.new(
        parsed_response: { 'devices' => [{ 'id' => 1, 'name' => 'MacBook' }], 'totalCount' => 1 },
        code: 200,
        success?: true,
        message: 'OK',
        body: '{"devices":[{"id":1,"name":"MacBook"}],"totalCount":1}'
      )

      allow(HTTParty).to receive(:post).and_return(fake_response)

      result = @service.get_devices(1, 'mac')
      expect(result).to eq(fake_response.parsed_response)

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'get_mac_devices',
        class_name: described_class.name
      )
      expect(event).to be_present
      expect(event.status).to eq('success')
    end

    it 'raises and logs an error when the API returns a failure' do
      fake_error = OpenStruct.new(
        parsed_response: {},
        response: OpenStruct.new(message: 'Not Found'),
        code: 404,
        success?: false,
        message: 'Not Found',
        body: ''
      )

      allow(HTTParty).to receive(:post).and_return(fake_error)

      expect {
        @service.get_devices(1, 'mac')
      }.to raise_error('Not Found')

      event = Logs::ApiEvent.find_by(
        company_id: @company.id,
        api_type: 'get_mac_devices',
        class_name: described_class.name
      )
      expect(event).to be_present
      expect(event.status).to eq('error')
    end
  end
end
