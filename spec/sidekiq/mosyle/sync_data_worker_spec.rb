require 'rails_helper'

RSpec.describe Integrations::Mosyle::SyncDataWorker, type: :worker do
  describe '#perform' do
    before(:each) do
      @company           = FactoryBot.create(:company)
      Integration.find_or_create_by!(name: 'mosyle')
      @mosyle_config     = FactoryBot.create(:mosyle_config, company: @company)
      @mosyle_config.create_comp_intg_and_execute_job

      @device = {
        "deviceudid"      => "E3EEAF83-BC56-5B96-80D3-157B7C41F871",
        "total_disk"      => "245.0000000000",
        "os"              => "mac",
        "serial_number"   => "C02G77KDQ05D",
        "device_name"     => "dev’s MacBook Pro",
        "device_model"    => "MacBookPro17,1",
        "battery"         => "0.91",
        "osversion"       => "13.4",
        "date_info"       => "1750270625",
        "carrier"         => nil,
        "roaming_enabled" => nil,
        "isroaming"       => nil,
        "available_disk"  => "30.0000000000",
        "wifi_mac_address"=> "3C:06:30:35:27:EB",
        "bluetooth_mac_address" => "00:00:00:00:00:00",
        "is_supervised"   => "1",
        "status"          => "INSTALLED",
        "device_type"     => "COMPUTER",
        "lostmode_status" => "DISABLED",
        "is_muted"        => "0",
        "activation_bypass" => nil,
        "imei"            => nil,
        "SystemIntegrityProtectionEnabled" => "1",
        "BuildVersion"    => "22F66",
        "LocalHostName"   => "devs-MacBook-Pro-2",
        "HostName"        => "devs-MacBook-Pro-2.local",
        "OpenDirectDeviceLink" => "https://mymsp.mosyle.com/#device_d_7_E3EEAF83-BC56-5B96-80D3-157B7C41F871"
      }
    end

    it 'discovers and saves a device from Mosyle and creates an asset source' do
      fetcher = Integrations::Mosyle::FetchData.new(@company.id, @mosyle_config)
      allow_any_instance_of(Integrations::Mosyle::FetchData)
        .to receive(:token)
        .and_return({ "refresh_token" => "ref123", "expires_in" => 3600 })
      allow_any_instance_of(Integrations::Mosyle::FetchData)
        .to receive(:get_devices)
        .and_return({
          'response' => [{
            'devices' => [@device],
            'totalCount' => 1
          }]
        })
      described_class.new.perform(@mosyle_config.id, true, false, nil)

      da = DiscoveredAsset.mosyle.ready_for_import.find_by(company_id: @company.id,
                                                           machine_serial_no: "C02G77KDQ05D")
      expect(da).to be_present
      expect(da.mac_addresses).to include("3C:06:30:35:27:EB")
      expect(da.asset_sources.where(source: 'mosyle').count).to eq(1)
    end

    it 'rescues exceptions and updates the integration status on failure' do
      allow_any_instance_of(Integrations::Mosyle::FetchData)
        .to receive(:get_devices)
        .and_raise("timeout")

      expect {
        described_class.new.perform(@mosyle_config.id, false, false, nil)
      }.not_to raise_error

      ci = @mosyle_config.company_integration.reload
      expect(ci.sync_status).to eq('failed')
      expect(ci.status).to eq(false)
      expect(ci.error_message).to match(/timeout/)
    end
  end
end
