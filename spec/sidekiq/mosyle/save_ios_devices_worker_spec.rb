require 'rails_helper'

RSpec.describe Integrations::Mosyle::SaveIosDevicesWorker, type: :worker do
  describe '#perform' do
    before(:each) do
      @company           = FactoryBot.create(:company)
      Integration.find_or_create_by!(name: 'mosyle')
      @mosyle_config     = FactoryBot.create(:mosyle_config, company: @company)
      @mosyle_config.create_comp_intg_and_execute_job

      @device = {
        "deviceudid"      => "00008110-0010285A0286601E",
        "total_disk"      => "128.0000000000",
        "os"              => "ios",
        "serial_number"   => "C6973N4V9G",
        "device_name"     => "iPhone",
        "device_model"    => "iPhone14,7",
        "battery"         => "0.24",
        "osversion"       => "18.5",
        "wifi_mac_address"=> "9C:FA:76:A3:EA:06",
        "bluetooth_mac_address" => "9C:FA:76:A8:66:3B",
        "status"          => "INSTALLED",
        "device_type"     => "SMARTPHONE",
        "is_muted"        => "0"
      }
    end

    it 'successfully saves an iOS device and creates an asset source' do
      fetcher = Integrations::Mosyle::FetchData.new(@company.id, @mosyle_config)
      allow_any_instance_of(Integrations::Mosyle::FetchData)
        .to receive(:token)
        .and_return({ "refresh_token" => "ref123", "expires_in" => 3600 })
      allow_any_instance_of(Integrations::Mosyle::FetchData)
        .to receive(:get_devices)
        .and_return({
          'response' => [{
            'devices' => [@device],
            'totalCount' => 1
          }]
        })
      described_class.new.perform(@mosyle_config.id, true, 0, false, nil)

      da = DiscoveredAsset.mosyle.ready_for_import.find_by(company_id: @company.id,
                                                           machine_serial_no: "C6973N4V9G")
      expect(da).to be_present
      expect(da.mac_addresses).to include("9C:FA:76:A3:EA:06")
      expect(da.asset_sources.where(source: 'mosyle').count).to eq(1)
    end

    it 'rescues exceptions and updates the integration status on failure' do
      allow_any_instance_of(Integrations::Mosyle::FetchData)
        .to receive(:get_devices)
        .and_raise("timeout")

      expect {
        described_class.new.perform(@mosyle_config.id, false, 0, false, nil)
      }.not_to raise_error

      ci = @mosyle_config.company_integration.reload
      expect(ci.sync_status).to eq('failed')
      expect(ci.status).to eq(false)
      expect(ci.error_message).to match(/timeout/)
    end
  end
end
